# 微信群聊内容智能分析系统设计文档

## 1. 系统架构设计

### 1.1 总体架构

```
┌─────────────────────────────────────────────────────────────┐
│                    微信群聊智能分析系统                        │
├─────────────────────────────────────────────────────────────┤
│  主程序层 (main.py)                                          │
│  ├─ 系统初始化                                               │
│  ├─ 流程控制                                                 │
│  ├─ 异常处理                                                 │
│  └─ 日志管理                                                 │
├─────────────────────────────────────────────────────────────┤
│  业务逻辑层                                                   │
│  ├─ 数据预处理模块 (preprocessing/)                          │
│  ├─ 智能分析模块 (analysis/)                                │
│  ├─ 深度分析模块 (deep_analysis/)                           │
│  └─ 消息通知模块 (notification/)                            │
├─────────────────────────────────────────────────────────────┤
│  数据访问层                                                   │
│  ├─ 数据库管理 (database/)                                  │
│  ├─ 配置管理 (config/)                                      │
│  └─ 数据模型 (models/)                                      │
├─────────────────────────────────────────────────────────────┤
│  外部接口层                                                   │
│  ├─ 豆包API                                                 │
│  ├─ 企业微信API                                             │
│  ├─ 股票数据API (可扩展)                                     │
│  └─ 新闻搜索API (可扩展)                                     │
└─────────────────────────────────────────────────────────────┘
```

### 1.2 模块设计

#### 1.2.1 配置管理模块 (config/)
**职责**：管理系统配置，支持环境变量和配置文件
**主要组件**：
- `ConfigManager`：配置管理器
- `config.yaml`：主配置文件
- `.env`：环境变量配置

#### 1.2.2 数据库模块 (database/)
**职责**：数据库连接、数据模型定义、数据访问操作
**主要组件**：
- `DatabaseManager`：数据库管理器
- `models.py`：数据模型定义
- `MessageData`：消息数据模型
- `AnalysisResult`：分析结果模型

#### 1.2.3 数据预处理模块 (preprocessing/)
**职责**：消息获取、过滤、清洗、格式化
**主要组件**：
- `DataProcessor`：数据处理器
- `MessageFilter`：消息过滤器

#### 1.2.4 智能分析模块 (analysis/)
**职责**：调用大模型API进行内容分析
**主要组件**：
- `DoubaoClient`：豆包API客户端
- `TopicAnalyzer`：话题分析器
- `SentimentAnalyzer`：情感分析器

#### 1.2.5 深度分析模块 (deep_analysis/)
**职责**：针对特定话题类型的专门分析
**主要组件**：
- `StockAnalyzer`：股票分析器
- `NewsAnalyzer`：新闻分析器
- `LinkAnalyzer`：链接分析器
- `GeneralAnalyzer`：通用分析器

#### 1.2.6 消息通知模块 (notification/)
**职责**：格式化分析结果并发送通知
**主要组件**：
- `WechatNotifier`：企业微信通知器
- `ReportFormatter`：报告格式化器

## 2. 数据流设计

### 2.1 主要数据流

```
微信数据库 → 数据预处理 → 智能分析 → 深度分析 → 结果存储 → 消息通知
    ↓           ↓          ↓         ↓         ↓         ↓
  MSG表     消息过滤    豆包API    专门分析   分析数据库  企业微信
           数据清洗    话题分类    股票/新闻              
           格式化      情感分析    链接解析              
```

### 2.2 详细数据流程

1. **数据获取阶段**
   - 从微信数据库MSG表读取最近1小时的消息
   - 按StrTalker字段分组
   - 只获取Type=1的文本消息

2. **数据预处理阶段**
   - 过滤系统消息、垃圾信息
   - 去除重复消息
   - 清洗消息内容
   - 提取关键词和实体

3. **智能分析阶段**
   - 调用豆包API进行话题分类
   - 进行情感分析
   - 提取关键信息
   - 评估讨论热度

4. **深度分析阶段**
   - 根据话题类型选择对应分析器
   - 股票话题：获取行情数据，生成投资建议
   - 时事话题：搜索相关资讯，分析影响
   - 链接话题：解析内容，生成摘要

5. **结果存储阶段**
   - 将分析结果保存到数据库
   - 更新群组信息
   - 记录分析统计数据

6. **消息通知阶段**
   - 格式化分析结果
   - 通过企业微信发送报告
   - 处理发送结果

## 3. 数据库设计

### 3.1 微信数据库表结构 (只读)

```sql
-- MSG表：微信消息表
CREATE TABLE MSG (
    localId         INTEGER PRIMARY KEY AUTOINCREMENT,
    TalkerId        INT     DEFAULT 0,
    MsgSvrID        INT,
    Type            INT,                    -- 消息类型，1为文本
    SubType         INT,
    IsSender        INT,
    CreateTime      INT,                    -- 创建时间戳
    Sequence        INT     DEFAULT 0,
    StrTalker       TEXT,                   -- 群组/用户标识
    StrContent      TEXT,                   -- 消息内容
    DisplayContent  TEXT,
    -- 其他字段...
);
```

### 3.2 分析结果数据库设计

```sql
-- 分析结果表
CREATE TABLE analysis_results (
    id                  INTEGER PRIMARY KEY AUTOINCREMENT,
    analysis_time       TEXT NOT NULL,      -- 分析时间
    group_id           TEXT NOT NULL,       -- 群组ID
    time_window_start  TEXT,               -- 时间窗口开始
    time_window_end    TEXT,               -- 时间窗口结束
    message_count      INTEGER DEFAULT 0,   -- 消息数量
    topics             TEXT,               -- 话题列表(JSON)
    main_topic         TEXT,               -- 主要话题
    topic_category     TEXT,               -- 话题分类
    sentiment_score    REAL DEFAULT 0.0,   -- 情感评分
    sentiment_label    TEXT,               -- 情感标签
    deep_analysis      TEXT,               -- 深度分析结果(JSON)
    overall_score      REAL DEFAULT 0.0,   -- 总体评分
    recommendations    TEXT,               -- 建议列表(JSON)
    raw_messages       TEXT,               -- 原始消息(JSON)
    ai_response        TEXT,               -- AI响应
    created_at         TEXT NOT NULL,      -- 创建时间
    updated_at         TEXT NOT NULL       -- 更新时间
);

-- 群组信息表
CREATE TABLE group_info (
    group_id           TEXT PRIMARY KEY,    -- 群组ID
    group_name         TEXT,               -- 群组名称
    member_count       INTEGER DEFAULT 0,   -- 成员数量
    last_message_time  TEXT,               -- 最后消息时间
    is_active          BOOLEAN DEFAULT 1,   -- 是否活跃
    created_at         TEXT NOT NULL       -- 创建时间
);

-- 索引
CREATE INDEX idx_analysis_group_time ON analysis_results(group_id, analysis_time);
CREATE INDEX idx_analysis_category ON analysis_results(topic_category);
CREATE INDEX idx_group_active ON group_info(is_active);
```

## 4. API设计

### 4.1 豆包API集成

```python
# API调用示例
{
    "model": "bot-20250709092323-lmvbj",
    "messages": [
        {
            "role": "system", 
            "content": "你是一个专业的群聊内容分析助手..."
        },
        {
            "role": "user", 
            "content": "请分析以下群聊内容：..."
        }
    ],
    "temperature": 0.7
}

# 期望响应格式
{
    "main_topic": "主要讨论话题",
    "topic_category": "股票|时事|链接|哲学感悟|其他",
    "topics": [...],
    "sentiment": {...},
    "key_information": [...],
    "discussion_heat": 8,
    "summary": "讨论摘要"
}
```

### 4.2 企业微信API集成

```python
# 消息发送格式
{
    "msgtype": "text",
    "text": {
        "content": "📊 微信群聊分析报告\n时间：2025-07-09 13:30:00\n\n..."
    }
}
```

## 5. 安全设计

### 5.1 数据安全
- API密钥通过环境变量管理
- 数据库连接使用安全配置
- 敏感信息不记录到日志

### 5.2 访问控制
- 只读访问微信数据库
- 分析结果数据库独立管理
- 配置文件权限控制

### 5.3 错误处理
- 完善的异常捕获机制
- API调用失败重试
- 降级处理方案

## 6. 性能设计

### 6.1 性能目标
- 单次分析时间：< 5分钟
- 消息处理能力：1000条/次
- 数据库查询：< 3秒

### 6.2 性能优化策略
- 数据库索引优化
- API调用并发控制
- 内存使用优化
- 缓存机制设计

## 7. 扩展性设计

### 7.1 模块扩展
- 插件化的深度分析器
- 可配置的数据源
- 灵活的通知渠道

### 7.2 功能扩展
- 支持更多消息类型
- 增加机器学习模型
- 多语言支持
- Web管理界面

## 8. 部署架构

### 8.1 单机部署
```
┌─────────────────┐
│   应用服务器     │
│  ┌───────────┐  │
│  │ Python应用 │  │
│  └───────────┘  │
│  ┌───────────┐  │
│  │ SQLite DB │  │
│  └───────────┘  │
│  ┌───────────┐  │
│  │   日志     │  │
│  └───────────┘  │
└─────────────────┘
```

### 8.2 分布式部署 (可扩展)
```
┌─────────────┐  ┌─────────────┐  ┌─────────────┐
│  Web界面     │  │  API服务     │  │  分析引擎    │
└─────────────┘  └─────────────┘  └─────────────┘
        │               │               │
        └───────────────┼───────────────┘
                        │
                ┌─────────────┐
                │  数据库集群  │
                └─────────────┘
```
