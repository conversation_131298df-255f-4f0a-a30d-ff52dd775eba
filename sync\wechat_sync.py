"""
微信数据同步器

使用pywxdump库实现微信数据的实时同步。
"""

import os
from typing import Tuple, Optional
from datetime import datetime
from loguru import logger

from config import get_config


class WechatDataSync:
    """微信数据同步器类"""
    
    def __init__(self):
        """初始化数据同步器"""
        self.config = get_config()
        
        # 从配置或环境变量获取同步参数
        self.sync_key = os.getenv('WXDUMP_KEY', 'ee1cbbc5204742e5b1c70fb634e4fcb92db785c9463a4f898b71715b4109e0b2')
        self.wx_path = os.getenv('WXDUMP_WX_PATH', 'C:\\Users\\<USER>\\Documents\\WeChat Files\\cheng4770\\Msg\\Multi')
        self.merge_path = self.config.get('database.wechat_db_path', 'D:\\wechat\\merge_all.db')
        
        # 验证路径
        self._validate_paths()
    
    def _validate_paths(self):
        """验证同步路径"""
        if not os.path.exists(os.path.dirname(self.merge_path)):
            logger.warning(f"合并数据库目录不存在: {os.path.dirname(self.merge_path)}")
        
        if not os.path.exists(self.wx_path):
            logger.warning(f"微信数据路径不存在: {self.wx_path}")
    
    def sync_data(self) -> Tuple[int, Optional[str]]:
        """
        同步微信数据
        
        Returns:
            (状态码, 错误信息)
            状态码: 0表示成功，非0表示失败
        """
        try:
            logger.info("开始同步微信数据...")
            start_time = datetime.now()
            
            # 动态导入pywxdump，避免在没有安装时导致整个模块无法加载
            try:
                from pywxdump import all_merge_real_time_db
            except ImportError as e:
                logger.error(f"pywxdump库未安装: {e}")
                return 1, "pywxdump库未安装，请运行: pip install pywxdump"
            
            # 执行数据同步
            code, ret = all_merge_real_time_db(
                key=self.sync_key,
                wx_path=self.wx_path,
                merge_path=self.merge_path
            )

            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()

            # 注意：pywxdump中code为True表示成功，False表示失败
            if code is True:
                logger.info(f"微信数据同步成功，耗时: {duration:.2f}秒")
                return 0, None
            else:
                error_msg = f"微信数据同步失败，错误信息: {ret}"
                logger.error(error_msg)
                return 1, error_msg
                
        except Exception as e:
            error_msg = f"微信数据同步异常: {str(e)}"
            logger.error(error_msg)
            return -1, error_msg
    
    def check_sync_requirements(self) -> Tuple[bool, str]:
        """
        检查同步环境要求
        
        Returns:
            (是否满足要求, 检查结果信息)
        """
        issues = []
        
        # 检查pywxdump库
        try:
            import pywxdump
            logger.debug(f"pywxdump版本: {getattr(pywxdump, '__version__', '未知')}")
        except ImportError:
            issues.append("pywxdump库未安装")
        
        # 检查微信路径
        if not os.path.exists(self.wx_path):
            issues.append(f"微信数据路径不存在: {self.wx_path}")
        
        # 检查合并数据库目录
        merge_dir = os.path.dirname(self.merge_path)
        if not os.path.exists(merge_dir):
            issues.append(f"合并数据库目录不存在: {merge_dir}")
        
        # 检查密钥
        if not self.sync_key or len(self.sync_key) < 32:
            issues.append("同步密钥无效")
        
        if issues:
            return False, "同步环境检查失败: " + "; ".join(issues)
        else:
            return True, "同步环境检查通过"
    
    def get_sync_config(self) -> dict:
        """
        获取同步配置信息
        
        Returns:
            同步配置字典
        """
        return {
            'sync_key': self.sync_key[:8] + "..." if self.sync_key else "未设置",
            'wx_path': self.wx_path,
            'merge_path': self.merge_path,
            'wx_path_exists': os.path.exists(self.wx_path),
            'merge_dir_exists': os.path.exists(os.path.dirname(self.merge_path))
        }
    
    def set_sync_config(self, sync_key: str = None, wx_path: str = None, merge_path: str = None):
        """
        设置同步配置
        
        Args:
            sync_key: 同步密钥
            wx_path: 微信数据路径
            merge_path: 合并数据库路径
        """
        if sync_key:
            self.sync_key = sync_key
            logger.info("同步密钥已更新")
        
        if wx_path:
            self.wx_path = wx_path
            logger.info(f"微信数据路径已更新: {wx_path}")
        
        if merge_path:
            self.merge_path = merge_path
            logger.info(f"合并数据库路径已更新: {merge_path}")
        
        # 重新验证路径
        self._validate_paths()
    
    def test_sync(self) -> bool:
        """
        测试同步功能
        
        Returns:
            测试是否成功
        """
        logger.info("开始测试微信数据同步...")
        
        # 检查环境要求
        requirements_ok, check_msg = self.check_sync_requirements()
        logger.info(f"环境检查结果: {check_msg}")
        
        if not requirements_ok:
            return False
        
        # 执行同步测试
        code, error_msg = self.sync_data()

        if code == 0:
            logger.info("✅ 微信数据同步测试成功")
            return True
        else:
            logger.error(f"❌ 微信数据同步测试失败: {error_msg}")
            return False
