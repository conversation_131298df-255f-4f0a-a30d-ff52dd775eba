#!/usr/bin/env python3
"""
微信群聊内容智能分析系统主程序

主要功能：
1. 从微信数据库获取最近1小时的消息
2. 使用豆包API进行智能分析
3. 进行深度分析（股票、时事、链接等）
4. 通过企业微信发送分析报告

作者：AI Assistant
版本：1.0.0
"""

import sys
import argparse
import traceback
from datetime import datetime
from typing import Dict, Any, List
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from loguru import logger
from config import get_config
from preprocessing.data_processor import DataProcessor
from analysis.topic_analyzer import TopicAnalyzer
from analysis.sentiment_analyzer import SentimentAnalyzer
from analysis.wordcloud_analyzer import WordCloudAnalyzer
from deep_analysis.stock_analyzer import StockAnalyzer
from deep_analysis.news_analyzer import NewsAnalyzer
from notification.wechat_notifier import WechatNotifier
from notification.report_formatter import ReportFormatter
from database.db_manager import DatabaseManager
from database.models import AnalysisResult
from sync.wechat_sync import WechatDataSync


class WechatAnalysisSystem:
    """微信群聊分析系统主类"""
    
    def __init__(self):
        """初始化系统"""
        self.config = get_config()
        self._setup_logging()
        
        # 初始化各模块
        self.data_processor = DataProcessor()
        self.topic_analyzer = TopicAnalyzer()
        self.sentiment_analyzer = SentimentAnalyzer()
        self.stock_analyzer = StockAnalyzer()
        self.news_analyzer = NewsAnalyzer()
        self.wechat_notifier = WechatNotifier()
        self.report_formatter = ReportFormatter()
        self.db_manager = DatabaseManager()
        self.wechat_sync = WechatDataSync()
        self.wordcloud_analyzer = WordCloudAnalyzer()
        
        logger.info("微信群聊分析系统初始化完成")
    
    def _setup_logging(self):
        """设置日志配置"""
        log_config = self.config.get_logging_config()
        
        # 移除默认处理器
        logger.remove()
        
        # 添加控制台输出
        logger.add(
            sys.stdout,
            level=log_config.get('level', 'INFO'),
            format=log_config.get('format', '{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}'),
            colorize=True
        )
        
        # 添加文件输出
        log_file = log_config.get('file_path', './logs/wx_analysis.log')
        Path(log_file).parent.mkdir(parents=True, exist_ok=True)
        
        logger.add(
            log_file,
            level=log_config.get('level', 'INFO'),
            format=log_config.get('format', '{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}'),
            rotation=f"{log_config.get('max_file_size', 10)} MB",
            retention=log_config.get('backup_count', 5),
            encoding='utf-8'
        )
    
    def run_analysis(self, hours: int = None, sync_data: bool = True,
                   generate_wordcloud: bool = True, send_wordcloud_notification: bool = False) -> Dict[str, Any]:
        """
        运行完整的分析流程

        Args:
            hours: 分析时间窗口（小时）
            sync_data: 是否在分析前同步数据
            generate_wordcloud: 是否生成词云
            send_wordcloud_notification: 是否发送词云通知

        Returns:
            分析结果摘要
        """
        try:
            logger.info("开始执行微信群聊分析")
            start_time = datetime.now()

            # 0. 数据同步（可选）
            if sync_data:
                logger.info("步骤0: 数据同步")
                sync_code, sync_error = self.wechat_sync.sync_data()
                if sync_code != 0:
                    logger.warning(f"数据同步失败: {sync_error}")
                    # 继续执行分析，不因同步失败而中断
                else:
                    logger.info("数据同步成功")
            
            # 1. 数据预处理
            logger.info("步骤1: 数据预处理")
            messages_by_group = self.data_processor.get_recent_group_messages(hours)
            
            if not messages_by_group:
                logger.warning("未获取到任何消息，分析结束")
                return {
                    'status': 'no_data',
                    'message': '未获取到任何消息',
                    'analysis_time': datetime.now().isoformat()
                }
            
            # 2. 智能分析
            logger.info("步骤2: 智能分析")
            analysis_results = []
            
            for group_id, messages in messages_by_group.items():
                try:
                    result = self._analyze_group_messages(group_id, messages)
                    if result:
                        analysis_results.append(result)
                except Exception as e:
                    logger.error(f"分析群组 {group_id} 失败: {e}")
                    continue
            
            # 3. 生成分析摘要
            logger.info("步骤3: 生成分析摘要")
            summary = self.data_processor.get_analysis_summary(messages_by_group)
            
            # 4. 生成词云（可选）
            wordcloud_results = {}
            if generate_wordcloud and messages_by_group:
                logger.info("步骤4: 生成词云")
                for group_id, messages in messages_by_group.items():
                    try:
                        # 提取消息文本
                        message_texts = [msg.str_content for msg in messages]

                        # 生成词云
                        wordcloud_result = self.wordcloud_analyzer.generate_wordcloud(
                            message_texts, group_id
                        )

                        if wordcloud_result.get('success'):
                            wordcloud_results[group_id] = wordcloud_result

                            # 发送词云图片（仅在启用通知时）
                            if send_wordcloud_notification:
                                self.wechat_notifier.send_wordcloud_image(group_id, wordcloud_result)

                    except Exception as e:
                        logger.error(f"生成群组 {group_id} 词云失败: {e}")

            # 5. 发送通知
            logger.info("步骤5: 发送通知")
            notification_sent = False
            try:
                notification_sent = self.wechat_notifier.send_analysis_report(
                    analysis_results, summary
                )
            except Exception as e:
                logger.error(f"发送通知失败: {e}")
            
            # 6. 生成执行摘要
            end_time = datetime.now()
            execution_summary = {
                'status': 'success',
                'start_time': start_time.isoformat(),
                'end_time': end_time.isoformat(),
                'duration_seconds': (end_time - start_time).total_seconds(),
                'processed_groups': len(messages_by_group),
                'analyzed_groups': len(analysis_results),
                'total_messages': sum(len(msgs) for msgs in messages_by_group.values()),
                'notification_sent': notification_sent,
                'wordcloud_generated': len(wordcloud_results),
                'wordcloud_results': wordcloud_results,
                'analysis_results': analysis_results,
                'summary': summary
            }
            
            logger.info(f"分析完成: 处理{len(messages_by_group)}个群组，分析{len(analysis_results)}个群组，耗时{execution_summary['duration_seconds']:.2f}秒")
            return execution_summary
            
        except Exception as e:
            logger.error(f"分析过程发生错误: {e}")
            logger.error(traceback.format_exc())
            
            # 发送错误通知
            try:
                self.wechat_notifier.send_error_notification(str(e), "主分析流程")
            except:
                pass
            
            return {
                'status': 'error',
                'error': str(e),
                'analysis_time': datetime.now().isoformat()
            }
    
    def _analyze_group_messages(self, group_id: str, messages: List) -> Dict[str, Any]:
        """分析单个群组的消息"""
        logger.debug(f"开始分析群组 {group_id}，消息数量: {len(messages)}")
        
        # 准备分析数据
        analysis_data = self.data_processor.prepare_analysis_data(group_id, messages)
        message_text = self.data_processor.format_messages_for_ai(analysis_data)
        
        # 话题分析
        topic_result = self.topic_analyzer.analyze_topics(message_text)
        
        # 情感分析
        sentiment_result = self.sentiment_analyzer.analyze_sentiment(message_text)
        
        # 深度分析
        deep_analysis = {}
        
        # 股票分析
        if topic_result['topic_category'] == 'stock':
            stock_mentions = self.topic_analyzer.extract_stock_mentions(
                topic_result.get('entities', {}),
                topic_result.get('key_information', [])
            )
            if stock_mentions:
                deep_analysis['stock_analysis'] = self.stock_analyzer.analyze_stock_discussion(
                    stock_mentions, topic_result.get('key_information', [])
                )

        # 新闻分析
        if topic_result['topic_category'] == 'news':
            news_events = self.topic_analyzer.extract_news_events(
                topic_result.get('entities', {}),
                topic_result.get('key_information', [])
            )
            if news_events:
                deep_analysis['news_analysis'] = self.news_analyzer.analyze_news_discussion(
                    news_events, topic_result.get('key_information', [])
                )
        
        # 生成详细的聊天摘要和用户统计
        messages_list = analysis_data.get('raw_messages', [])
        chat_summary = self._generate_chat_summary(messages_list, topic_result, sentiment_result)
        user_statistics = self._generate_user_statistics(messages_list)

        # 构建分析结果
        result = AnalysisResult(
            group_id=group_id,
            time_window_start=analysis_data['time_range']['start'] if analysis_data['time_range'] else None,
            time_window_end=analysis_data['time_range']['end'] if analysis_data['time_range'] else None,
            message_count=analysis_data['message_count'],
            main_topic=topic_result.get('main_topic', ''),
            topic_category=topic_result.get('topic_category', 'other'),
            sentiment_score=sentiment_result.get('score', 0.0),
            sentiment_label=sentiment_result.get('label', 'neutral'),
            deep_analysis=deep_analysis,
            raw_messages=analysis_data.get('raw_messages', []),
            chat_summary=chat_summary,
            user_statistics=user_statistics
        )
        
        # 添加话题信息
        for topic_info in topic_result.get('topics', []):
            result.add_topic(
                topic_info.get('topic', ''),
                topic_info.get('confidence', 1.0),
                topic_info.get('keywords', [])
            )
        
        # 添加关键信息作为建议
        for info in topic_result.get('key_information', []):
            result.add_recommendation(info, 'medium')
        
        # 保存到数据库
        try:
            result_id = self.db_manager.save_analysis_result(result)
            logger.debug(f"群组 {group_id} 分析结果已保存，ID: {result_id}")
        except Exception as e:
            logger.warning(f"保存分析结果失败: {e}")
        
        # 转换为字典格式返回
        result_dict = result.to_dict()
        result_dict.update({
            'discussion_heat': topic_result.get('discussion_heat', 5),
            'key_information': topic_result.get('key_information', []),
            'entities': topic_result.get('entities', {}),
            'summary': topic_result.get('summary', '')
        })
        
        return result_dict
    
    def test_system(self) -> bool:
        """测试系统各组件"""
        logger.info("开始系统测试")
        
        tests = [
            ("数据库连接", self._test_database),
            ("豆包API连接", self._test_doubao_api),
            ("企业微信连接", self._test_wechat_notification),
            ("数据同步功能", self._test_data_sync)
        ]
        
        all_passed = True
        
        for test_name, test_func in tests:
            try:
                logger.info(f"测试: {test_name}")
                result = test_func()
                if result:
                    logger.info(f"✅ {test_name} 测试通过")
                else:
                    logger.error(f"❌ {test_name} 测试失败")
                    all_passed = False
            except Exception as e:
                logger.error(f"❌ {test_name} 测试异常: {e}")
                all_passed = False
        
        if all_passed:
            logger.info("🎉 所有测试通过")
        else:
            logger.error("⚠️ 部分测试失败")
        
        return all_passed
    
    def _test_database(self) -> bool:
        """测试数据库连接"""
        try:
            # 测试微信数据库连接
            messages = self.db_manager.get_recent_messages(hours=0.1)  # 测试获取6分钟内的消息
            
            # 测试分析数据库连接
            groups = self.db_manager.get_active_groups()
            
            return True
        except Exception as e:
            logger.error(f"数据库测试失败: {e}")
            return False
    
    def _test_doubao_api(self) -> bool:
        """测试豆包API连接"""
        try:
            return self.topic_analyzer.doubao_client.test_connection()
        except Exception as e:
            logger.error(f"豆包API测试失败: {e}")
            return False
    
    def _test_wechat_notification(self) -> bool:
        """测试企业微信通知"""
        try:
            return self.wechat_notifier.test_connection()
        except Exception as e:
            logger.error(f"企业微信测试失败: {e}")
            return False

    def _test_data_sync(self) -> bool:
        """测试数据同步功能"""
        try:
            return self.wechat_sync.test_sync()
        except Exception as e:
            logger.error(f"数据同步测试失败: {e}")
            return False

    def _generate_chat_summary(self, messages: List, topic_result: Dict[str, Any],
                             sentiment_result: Dict[str, Any]) -> List[str]:
        """
        生成详细的聊天摘要

        Args:
            messages: 消息列表
            topic_result: 话题分析结果
            sentiment_result: 情感分析结果

        Returns:
            聊天摘要列表
        """
        summary = []

        if not messages:
            return summary

        # 时间范围摘要
        start_time = min(msg.create_datetime for msg in messages)
        end_time = max(msg.create_datetime for msg in messages)
        duration = (end_time - start_time).total_seconds() / 3600  # 小时
        summary.append(f"讨论时间跨度: {duration:.1f}小时，共{len(messages)}条消息")

        # 话题摘要
        main_topic = topic_result.get('main_topic', '未知话题')
        discussion_heat = topic_result.get('discussion_heat', 1)
        summary.append(f"主要讨论话题: {main_topic}，讨论热度: {discussion_heat}/10")

        # 情感摘要
        sentiment_score = sentiment_result.get('score', 0.0)
        sentiment_label = sentiment_result.get('label', 'neutral')
        if sentiment_score > 0.3:
            sentiment_desc = "整体氛围积极"
        elif sentiment_score < -0.3:
            sentiment_desc = "整体氛围消极"
        else:
            sentiment_desc = "整体氛围中性"
        summary.append(f"情感倾向: {sentiment_desc} (评分: {sentiment_score:.2f})")

        # 关键内容摘要
        key_info = topic_result.get('key_information', [])
        if key_info:
            summary.append(f"关键讨论点: {'; '.join(key_info[:5])}")

        # 消息内容摘要（新增）
        if len(messages) > 0:
            # 提取最活跃的讨论片段
            message_contents = [msg.str_content for msg in messages if msg.str_content]
            if message_contents:
                # 按长度排序，取较长的消息作为代表性内容
                long_messages = [msg for msg in message_contents if len(msg) > 10]
                if long_messages:
                    long_messages.sort(key=len, reverse=True)
                    representative_content = long_messages[:3]
                    summary.append(f"代表性讨论内容: {'; '.join([msg[:50] + '...' if len(msg) > 50 else msg for msg in representative_content])}")

        # 讨论频率分析（新增）
        if len(messages) > 1:
            time_intervals = []
            sorted_messages = sorted(messages, key=lambda x: x.create_datetime)
            for i in range(1, len(sorted_messages)):
                interval = (sorted_messages[i].create_datetime - sorted_messages[i-1].create_datetime).total_seconds() / 60
                time_intervals.append(interval)

            if time_intervals:
                avg_interval = sum(time_intervals) / len(time_intervals)
                if avg_interval < 1:
                    frequency_desc = "讨论非常活跃（平均间隔<1分钟）"
                elif avg_interval < 5:
                    frequency_desc = "讨论较为活跃（平均间隔<5分钟）"
                elif avg_interval < 15:
                    frequency_desc = "讨论适中（平均间隔<15分钟）"
                else:
                    frequency_desc = "讨论较为稀疏（平均间隔>15分钟）"
                summary.append(f"讨论频率: {frequency_desc}")

        return summary

    def _generate_user_statistics(self, messages: List) -> Dict[str, Any]:
        """
        生成用户参与统计

        Args:
            messages: 消息列表

        Returns:
            用户统计信息
        """
        if not messages:
            return {}

        # 统计用户参与情况
        user_message_count = {}
        for msg in messages:
            # 这里假设有用户ID字段，实际需要根据数据结构调整
            user_id = getattr(msg, 'talker_id', 'unknown')
            user_message_count[user_id] = user_message_count.get(user_id, 0) + 1

        # 计算统计信息
        active_users = len(user_message_count)
        total_messages = len(messages)
        avg_messages_per_user = total_messages / active_users if active_users > 0 else 0

        # 找出最活跃的用户
        most_active_user = max(user_message_count.items(), key=lambda x: x[1]) if user_message_count else None

        return {
            'active_users': active_users,
            'total_messages': total_messages,
            'avg_messages_per_user': round(avg_messages_per_user, 1),
            'most_active_user': most_active_user[0] if most_active_user else None,
            'most_active_user_count': most_active_user[1] if most_active_user else 0
        }


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='微信群聊内容智能分析系统')
    parser.add_argument('--hours', type=int, default=1, help='分析时间窗口（小时）')
    parser.add_argument('--test', action='store_true', help='运行系统测试')
    parser.add_argument('--schedule', action='store_true', help='启用定时任务模式')
    parser.add_argument('--no-sync', action='store_true', help='跳过数据同步')
    parser.add_argument('--no-wordcloud', action='store_true', help='跳过词云生成')
    parser.add_argument('--web', action='store_true', help='启动Web管理界面')
    parser.add_argument('--port', type=int, default=8000, help='Web服务器端口')
    
    args = parser.parse_args()
    
    try:
        system = WechatAnalysisSystem()
        
        if args.test:
            # 运行测试
            success = system.test_system()
            sys.exit(0 if success else 1)

        elif args.web:
            # Web服务器模式
            logger.info(f"启动Web管理界面: http://localhost:{args.port}")
            try:
                import uvicorn
                from web.api_server import create_app

                app = create_app()
                uvicorn.run(app, host="0.0.0.0", port=args.port)
            except ImportError:
                logger.error("Web依赖未安装，请运行: pip install fastapi uvicorn")
            except Exception as e:
                logger.error(f"Web服务器启动失败: {e}")

        elif args.schedule:
            # 定时任务模式（简单实现）
            import time
            logger.info("启动定时任务模式")
            
            while True:
                try:
                    result = system.run_analysis(
                        args.hours,
                        sync_data=not args.no_sync,
                        generate_wordcloud=not args.no_wordcloud
                    )
                    logger.info(f"定时分析完成: {result.get('status')}")

                    # 等待1小时
                    time.sleep(3600)
                    
                except KeyboardInterrupt:
                    logger.info("收到中断信号，退出定时任务")
                    break
                except Exception as e:
                    logger.error(f"定时任务执行错误: {e}")
                    time.sleep(300)  # 出错后等待5分钟再重试
        
        else:
            # 单次运行模式
            result = system.run_analysis(
                args.hours,
                sync_data=not args.no_sync,
                generate_wordcloud=not args.no_wordcloud
            )
            
            if result['status'] == 'success':
                print(f"✅ 分析完成: 处理{result['processed_groups']}个群组，分析{result['analyzed_groups']}个群组")
                sys.exit(0)
            else:
                print(f"❌ 分析失败: {result.get('error', result.get('message', '未知错误'))}")
                sys.exit(1)
    
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        logger.error(traceback.format_exc())
        sys.exit(1)


if __name__ == '__main__':
    main()
