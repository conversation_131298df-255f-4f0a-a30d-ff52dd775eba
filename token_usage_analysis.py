#!/usr/bin/env python3
"""
Token使用情况分析工具

分析当前系统中大模型API的token使用情况，识别优化机会。
"""

import sys
import os
import re
from typing import Dict, Any, List, Tuple
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def analyze_prompt_token_usage():
    """分析prompt的token使用情况"""
    print("🔍 分析当前系统的Token使用情况...")
    print("=" * 60)
    
    # 分析豆包客户端的prompt
    doubao_prompts = {
        "analyze_topic_and_sentiment": {
            "system_prompt": """你是一个专业的群聊内容分析助手。请分析给定的群聊消息，并按照以下JSON格式返回结果：

{
    "main_topic": "主要讨论话题的简短描述",
    "topic_category": "话题分类，必须是以下之一：股票、时事、链接、哲学感悟、其他",
    "topics": [
        {
            "topic": "具体话题",
            "confidence": 0.9,
            "keywords": ["关键词1", "关键词2"]
        }
    ],
    "sentiment": {
        "score": 0.2,
        "label": "情感标签：积极、消极、中性",
        "explanation": "情感分析的简要说明"
    },
    "key_information": [
        "提取的关键信息1",
        "提取的关键信息2"
    ],
    "discussion_heat": 8,
    "summary": "对整个讨论的简要总结"
}

分析要求：
1. topic_category必须准确分类
2. sentiment.score范围为-1到1，-1最消极，1最积极
3. discussion_heat范围为1-10，表示讨论热度
4. 提取真正有价值的关键信息
5. 如果涉及股票，请识别股票代码或名称
6. 如果涉及链接，请标注链接内容
7. 返回标准JSON格式，不要包含其他文本""",
            "estimated_tokens": 450
        },
        
        "extract_entities": {
            "system_prompt": """请从给定的群聊消息中提取以下类型的实体，并以JSON格式返回：

{
    "stocks": ["股票代码或名称"],
    "companies": ["公司名称"],
    "persons": ["人名"],
    "locations": ["地点"],
    "urls": ["网址链接"],
    "dates": ["日期时间"],
    "numbers": ["重要数字"],
    "events": ["事件名称"]
}

提取要求：
1. 只提取确实存在的实体
2. 股票代码格式如：000001、SH600000、AAPL等
3. 公司名称要完整准确
4. 网址要完整
5. 如果某类实体不存在，返回空数组
6. 返回标准JSON格式""",
            "estimated_tokens": 280
        },
        
        "generate_summary": {
            "system_prompt_template": """请为给定的群聊消息生成一个简洁的摘要，要求：

1. 摘要长度不超过{max_length}字
2. 突出主要讨论内容和关键信息
3. 保持客观中性的语调
4. 如果涉及股票、时事等重要话题，要重点体现
5. 直接返回摘要内容，不要包含其他格式""",
            "estimated_tokens": 120
        }
    }
    
    print("📊 当前Prompt Token使用分析:")
    total_system_tokens = 0
    
    for method_name, prompt_info in doubao_prompts.items():
        tokens = prompt_info["estimated_tokens"]
        total_system_tokens += tokens
        print(f"  {method_name}: ~{tokens} tokens")
    
    print(f"\n总系统Prompt Token: ~{total_system_tokens} tokens")
    
    # 估算用户输入token
    print(f"\n📝 用户输入Token估算:")
    print(f"  平均群聊消息长度: ~500-2000字符")
    print(f"  估算Token数量: ~200-800 tokens")
    print(f"  单次完整分析: ~{total_system_tokens + 500} tokens")
    
    return {
        "system_prompts": doubao_prompts,
        "total_system_tokens": total_system_tokens,
        "estimated_user_tokens": 500,
        "total_per_analysis": total_system_tokens + 500
    }

def identify_optimization_opportunities():
    """识别优化机会"""
    print("\n🎯 Token优化机会分析:")
    print("=" * 60)
    
    opportunities = [
        {
            "area": "System Prompt优化",
            "current_issue": "Prompt过于详细，包含大量示例和说明",
            "optimization": "精简指令，移除冗余说明",
            "potential_savings": "30-40%"
        },
        {
            "area": "输入文本预处理",
            "current_issue": "直接发送原始群聊内容，可能包含重复和无关信息",
            "optimization": "预处理过滤、去重、摘要",
            "potential_savings": "20-50%"
        },
        {
            "area": "分批处理",
            "current_issue": "单次处理大量文本",
            "optimization": "分割成小块处理，只处理关键部分",
            "potential_savings": "40-60%"
        },
        {
            "area": "缓存机制",
            "current_issue": "相似内容重复分析",
            "optimization": "实施内容哈希缓存",
            "potential_savings": "50-80%"
        },
        {
            "area": "智能过滤",
            "current_issue": "分析所有消息，包括无价值内容",
            "optimization": "预先过滤无价值消息",
            "potential_savings": "30-70%"
        }
    ]
    
    for i, opp in enumerate(opportunities, 1):
        print(f"{i}. {opp['area']}")
        print(f"   问题: {opp['current_issue']}")
        print(f"   优化: {opp['optimization']}")
        print(f"   预期节省: {opp['potential_savings']}")
        print()
    
    return opportunities

def estimate_optimization_impact():
    """估算优化影响"""
    print("📈 优化效果预估:")
    print("=" * 60)
    
    current_usage = {
        "single_analysis": 950,  # 系统prompt + 用户输入
        "daily_analyses": 100,   # 假设每天100次分析
        "monthly_cost": "假设每1K token = $0.002"
    }
    
    optimized_usage = {
        "single_analysis": 400,  # 优化后预估
        "daily_analyses": 100,
        "reduction": "58%"
    }
    
    print(f"当前使用:")
    print(f"  单次分析: ~{current_usage['single_analysis']} tokens")
    print(f"  每日分析: {current_usage['daily_analyses']} 次")
    print(f"  每日总计: ~{current_usage['single_analysis'] * current_usage['daily_analyses']:,} tokens")
    
    print(f"\n优化后:")
    print(f"  单次分析: ~{optimized_usage['single_analysis']} tokens")
    print(f"  每日分析: {optimized_usage['daily_analyses']} 次")
    print(f"  每日总计: ~{optimized_usage['single_analysis'] * optimized_usage['daily_analyses']:,} tokens")
    print(f"  节省比例: {optimized_usage['reduction']}")
    
    daily_savings = (current_usage['single_analysis'] - optimized_usage['single_analysis']) * current_usage['daily_analyses']
    monthly_savings = daily_savings * 30
    
    print(f"\n💰 成本节省:")
    print(f"  每日节省: ~{daily_savings:,} tokens")
    print(f"  每月节省: ~{monthly_savings:,} tokens")
    print(f"  成本节省: ~${monthly_savings * 0.002:.2f}/月 (假设$0.002/1K tokens)")
    
    return {
        "current_daily_tokens": current_usage['single_analysis'] * current_usage['daily_analyses'],
        "optimized_daily_tokens": optimized_usage['single_analysis'] * optimized_usage['daily_analyses'],
        "daily_savings": daily_savings,
        "monthly_savings": monthly_savings
    }

def main():
    """主函数"""
    print("🚀 Token使用情况分析报告")
    print(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # 1. 分析当前token使用情况
    usage_analysis = analyze_prompt_token_usage()
    
    # 2. 识别优化机会
    opportunities = identify_optimization_opportunities()
    
    # 3. 估算优化影响
    impact = estimate_optimization_impact()
    
    print("\n🎯 优化建议优先级:")
    print("=" * 60)
    print("1. 高优先级: System Prompt精简 (立即实施)")
    print("2. 高优先级: 输入文本预处理 (立即实施)")
    print("3. 中优先级: 缓存机制 (短期实施)")
    print("4. 中优先级: 智能过滤 (短期实施)")
    print("5. 低优先级: 分批处理 (长期优化)")
    
    print(f"\n✅ 分析完成！预期可节省 {impact['daily_savings']:,} tokens/天")
    
    return {
        "usage_analysis": usage_analysis,
        "opportunities": opportunities,
        "impact": impact
    }

if __name__ == "__main__":
    main()
