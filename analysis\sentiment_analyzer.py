"""
情感分析器

负责消息情感的分析和评估。
"""

from typing import Dict, Any, List, Optional
from datetime import datetime
from loguru import logger

from .doubao_client import DoubaoClient


class SentimentAnalyzer:
    """情感分析器类"""
    
    def __init__(self):
        """初始化情感分析器"""
        self.doubao_client = DoubaoClient()
        
        # 情感标签映射
        self.sentiment_mapping = {
            '积极': 'positive',
            '消极': 'negative', 
            '中性': 'neutral'
        }
    
    def analyze_sentiment(self, message_text: str) -> Dict[str, Any]:
        """
        分析消息情感
        
        Args:
            message_text: 消息文本
            
        Returns:
            情感分析结果
        """
        try:
            logger.info("开始情感分析")
            
            # 调用豆包API进行分析
            analysis_result = self.doubao_client.analyze_topic_and_sentiment(message_text)
            
            # 提取情感信息
            sentiment_info = analysis_result.get('sentiment', {})
            
            # 标准化情感标签
            original_label = sentiment_info.get('label', '中性')
            standardized_label = self.sentiment_mapping.get(original_label, 'neutral')
            
            # 构建结果
            result = {
                'score': sentiment_info.get('score', 0.0),
                'label': standardized_label,
                'original_label': original_label,
                'explanation': sentiment_info.get('explanation', ''),
                'confidence': self._calculate_confidence(sentiment_info.get('score', 0.0)),
                'analysis_time': datetime.now().isoformat()
            }
            
            logger.info(f"情感分析完成: 标签={standardized_label}, 评分={result['score']}")
            return result
            
        except Exception as e:
            logger.error(f"情感分析失败: {e}")
            return {
                'score': 0.0,
                'label': 'neutral',
                'original_label': '中性',
                'explanation': f'情感分析失败: {str(e)}',
                'confidence': 0.0,
                'analysis_time': datetime.now().isoformat()
            }
    
    def _calculate_confidence(self, score: float) -> float:
        """
        根据情感评分计算置信度
        
        Args:
            score: 情感评分 (-1 到 1)
            
        Returns:
            置信度 (0 到 1)
        """
        # 置信度基于评分的绝对值
        return min(abs(score), 1.0)
    
    def classify_sentiment_intensity(self, score: float) -> str:
        """
        根据评分分类情感强度
        
        Args:
            score: 情感评分
            
        Returns:
            情感强度等级
        """
        abs_score = abs(score)
        
        if abs_score >= 0.7:
            return 'strong'
        elif abs_score >= 0.4:
            return 'moderate'
        elif abs_score >= 0.1:
            return 'weak'
        else:
            return 'neutral'
    
    def analyze_group_sentiment_trend(self, sentiment_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        分析群组情感趋势
        
        Args:
            sentiment_results: 情感分析结果列表
            
        Returns:
            情感趋势分析
        """
        if not sentiment_results:
            return {
                'overall_sentiment': 'neutral',
                'average_score': 0.0,
                'sentiment_distribution': {},
                'trend': 'stable',
                'volatility': 0.0
            }
        
        # 计算平均情感评分
        scores = [result.get('score', 0.0) for result in sentiment_results]
        average_score = sum(scores) / len(scores)
        
        # 统计情感分布
        labels = [result.get('label', 'neutral') for result in sentiment_results]
        distribution = {}
        for label in labels:
            distribution[label] = distribution.get(label, 0) + 1
        
        # 转换为百分比
        total = len(labels)
        for label in distribution:
            distribution[label] = round(distribution[label] / total * 100, 1)
        
        # 确定整体情感
        if average_score > 0.2:
            overall_sentiment = 'positive'
        elif average_score < -0.2:
            overall_sentiment = 'negative'
        else:
            overall_sentiment = 'neutral'
        
        # 计算情感波动性
        if len(scores) > 1:
            variance = sum((score - average_score) ** 2 for score in scores) / len(scores)
            volatility = variance ** 0.5
        else:
            volatility = 0.0
        
        # 分析趋势
        trend = self._analyze_sentiment_trend(scores)
        
        return {
            'overall_sentiment': overall_sentiment,
            'average_score': round(average_score, 3),
            'sentiment_distribution': distribution,
            'trend': trend,
            'volatility': round(volatility, 3),
            'sample_size': total
        }
    
    def _analyze_sentiment_trend(self, scores: List[float]) -> str:
        """
        分析情感评分趋势
        
        Args:
            scores: 情感评分列表
            
        Returns:
            趋势描述
        """
        if len(scores) < 3:
            return 'stable'
        
        # 计算前半部分和后半部分的平均值
        mid_point = len(scores) // 2
        first_half_avg = sum(scores[:mid_point]) / mid_point
        second_half_avg = sum(scores[mid_point:]) / (len(scores) - mid_point)
        
        diff = second_half_avg - first_half_avg
        
        if diff > 0.1:
            return 'improving'
        elif diff < -0.1:
            return 'declining'
        else:
            return 'stable'
    
    def generate_sentiment_insights(self, sentiment_analysis: Dict[str, Any]) -> List[str]:
        """
        生成情感洞察
        
        Args:
            sentiment_analysis: 情感分析结果
            
        Returns:
            洞察列表
        """
        insights = []
        
        overall = sentiment_analysis.get('overall_sentiment', 'neutral')
        average_score = sentiment_analysis.get('average_score', 0.0)
        distribution = sentiment_analysis.get('sentiment_distribution', {})
        trend = sentiment_analysis.get('trend', 'stable')
        volatility = sentiment_analysis.get('volatility', 0.0)
        
        # 整体情感洞察
        if overall == 'positive':
            insights.append(f"群组整体情感积极，平均评分 {average_score:.2f}")
        elif overall == 'negative':
            insights.append(f"群组整体情感消极，平均评分 {average_score:.2f}")
        else:
            insights.append(f"群组整体情感中性，平均评分 {average_score:.2f}")
        
        # 情感分布洞察
        if distribution:
            dominant_sentiment = max(distribution.items(), key=lambda x: x[1])
            insights.append(f"主导情感为{dominant_sentiment[0]}，占比 {dominant_sentiment[1]}%")
        
        # 趋势洞察
        if trend == 'improving':
            insights.append("讨论过程中情感趋向积极")
        elif trend == 'declining':
            insights.append("讨论过程中情感趋向消极")
        else:
            insights.append("讨论过程中情感保持稳定")
        
        # 波动性洞察
        if volatility > 0.5:
            insights.append("情感波动较大，讨论较为激烈")
        elif volatility > 0.3:
            insights.append("情感波动适中，讨论相对平和")
        else:
            insights.append("情感波动较小，讨论氛围稳定")
        
        return insights
    
    def get_sentiment_recommendations(self, sentiment_analysis: Dict[str, Any]) -> List[str]:
        """
        根据情感分析生成建议
        
        Args:
            sentiment_analysis: 情感分析结果
            
        Returns:
            建议列表
        """
        recommendations = []
        
        overall = sentiment_analysis.get('overall_sentiment', 'neutral')
        volatility = sentiment_analysis.get('volatility', 0.0)
        trend = sentiment_analysis.get('trend', 'stable')
        
        # 基于整体情感的建议
        if overall == 'negative':
            recommendations.append("群组情感偏消极，建议关注成员情绪，适时引导正面讨论")
        elif overall == 'positive':
            recommendations.append("群组情感积极，可以继续保持良好的讨论氛围")
        
        # 基于波动性的建议
        if volatility > 0.5:
            recommendations.append("情感波动较大，建议关注争议话题，适时调节讨论节奏")
        
        # 基于趋势的建议
        if trend == 'declining':
            recommendations.append("情感趋势下降，建议关注讨论内容，避免负面情绪扩散")
        elif trend == 'improving':
            recommendations.append("情感趋势向好，可以继续当前的讨论方向")
        
        return recommendations
