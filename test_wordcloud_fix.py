#!/usr/bin/env python3
"""
测试词云发送修复
"""

import sys
import os
import time
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_wordcloud_with_real_data():
    """使用真实数据测试词云生成和发送"""
    print("🧪 使用真实数据测试词云生成和发送...")
    
    try:
        from database.db_manager import DatabaseManager
        from analysis.wordcloud_analyzer import WordCloudAnalyzer
        from notification.wechat_notifier import WechatNotifier
        
        # 获取真实数据
        db_manager = DatabaseManager()
        messages = db_manager.get_recent_messages(24)  # 获取24小时内的消息
        
        if not messages:
            print("❌ 没有找到消息数据")
            return False
        
        print(f"找到 {len(messages)} 个群组的消息")
        
        # 选择第一个有消息的群组进行测试
        test_group_id = None
        test_messages = None
        
        for group_id, group_messages in messages.items():
            if len(group_messages) >= 5:  # 至少5条消息
                test_group_id = group_id
                test_messages = [msg.str_content for msg in group_messages]
                break
        
        if not test_group_id:
            print("❌ 没有找到足够消息的群组")
            return False
        
        print(f"使用群组 {test_group_id[:20]}... 进行测试")
        print(f"消息数量: {len(test_messages)}")
        
        # 测试词云生成
        analyzer = WordCloudAnalyzer()
        print("生成词云...")
        wordcloud_result = analyzer.generate_wordcloud(test_messages, test_group_id)
        
        print(f"词云生成结果:")
        print(f"  success: {wordcloud_result.get('success')}")
        print(f"  word_count: {wordcloud_result.get('word_count', 0)}")
        print(f"  has_base64: {'base64' in wordcloud_result}")
        print(f"  error: {wordcloud_result.get('error', 'None')}")
        
        if not wordcloud_result.get('success'):
            print("❌ 词云生成失败")
            return False
        
        # 测试词云发送
        notifier = WechatNotifier()
        
        # 记录发送
        send_count = 0
        original_send_raw = notifier._send_message_raw
        
        def mock_send_raw(message):
            nonlocal send_count
            send_count += 1
            msg_type = message.get('msgtype')
            print(f"模拟发送第{send_count}条消息: {msg_type}")
            if msg_type == 'image':
                print(f"  图片MD5: {message.get('image', {}).get('md5', 'None')}")
            elif msg_type == 'text':
                content = message.get('text', {}).get('content', '')
                print(f"  文字内容: {content[:50]}...")
            return True
        
        notifier._send_message_raw = mock_send_raw
        
        print("发送词云...")
        send_result = notifier.send_wordcloud_image(test_group_id, wordcloud_result)
        
        # 恢复原始方法
        notifier._send_message_raw = original_send_raw
        
        print(f"发送结果: {send_result}")
        print(f"发送次数: {send_count}")
        
        if send_result and send_count >= 1:
            print("✅ 词云生成和发送测试成功")
            return True
        else:
            print("❌ 词云发送失败")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_task_manager_wordcloud_logic():
    """测试任务管理器的词云逻辑"""
    print("\n🧪 测试任务管理器的词云逻辑...")
    
    try:
        from web.task_manager import AsyncTaskManager
        from notification.wechat_notifier import WechatNotifier
        from analysis.wordcloud_analyzer import WordCloudAnalyzer
        
        # 记录词云生成和发送
        wordcloud_generates = []
        wordcloud_sends = []
        
        original_generate = WordCloudAnalyzer.generate_wordcloud
        original_send = WechatNotifier.send_wordcloud_image
        
        def mock_generate(self, messages, group_id):
            result = original_generate(self, messages, group_id)
            wordcloud_generates.append({
                'group_id': group_id,
                'message_count': len(messages),
                'success': result.get('success'),
                'error': result.get('error')
            })
            print(f"词云生成: 群组{group_id[:20]}..., 消息{len(messages)}条, 成功: {result.get('success')}")
            return result
        
        def mock_send(self, group_id, wordcloud_result):
            wordcloud_sends.append({
                'group_id': group_id,
                'success': wordcloud_result.get('success')
            })
            print(f"词云发送: 群组{group_id[:20]}..., 成功: {wordcloud_result.get('success')}")
            return True
        
        WordCloudAnalyzer.generate_wordcloud = mock_generate
        WechatNotifier.send_wordcloud_image = mock_send
        
        task_manager = AsyncTaskManager()
        
        print("创建词云任务...")
        task_id = task_manager.create_analysis_task(
            hours=1,  # 使用1小时减少处理时间
            sync_data=False,
            generate_wordcloud=True,
            send_wordcloud_notification=True,
            send_analysis_report=False
        )
        
        # 等待任务完成
        print("等待任务完成...")
        for i in range(60):
            task_info = task_manager.get_task_info(task_id)
            if task_info and task_info.status.name in ['COMPLETED', 'FAILED']:
                print(f"任务状态: {task_info.status.name}")
                break
            time.sleep(1)
        else:
            print("任务超时")
        
        # 恢复原始方法
        WordCloudAnalyzer.generate_wordcloud = original_generate
        WechatNotifier.send_wordcloud_image = original_send
        
        print(f"词云生成次数: {len(wordcloud_generates)}")
        print(f"词云发送次数: {len(wordcloud_sends)}")
        
        # 显示详细结果
        for gen in wordcloud_generates:
            group_id = gen['group_id']
            success = gen['success']
            error = gen.get('error')
            print(f"  生成 {group_id[:20]}...: 成功={success}, 错误={error}")
        
        for send in wordcloud_sends:
            group_id = send['group_id']
            success = send['success']
            print(f"  发送 {group_id[:20]}...: 成功={success}")
        
        if len(wordcloud_generates) > 0 and len(wordcloud_sends) > 0:
            print("✅ 任务管理器词云逻辑正常")
            return True
        elif len(wordcloud_generates) > 0:
            print("⚠️ 词云生成了但没有发送")
            return False
        else:
            print("❌ 没有生成词云")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_wordcloud_config():
    """检查词云配置"""
    print("\n🧪 检查词云配置...")
    
    try:
        from analysis.wordcloud_analyzer import WordCloudAnalyzer
        from notification.wechat_notifier import WechatNotifier
        
        # 检查词云分析器
        analyzer = WordCloudAnalyzer()
        print(f"词云分析器:")
        print(f"  输出目录: {analyzer.output_dir}")
        print(f"  输出目录存在: {analyzer.output_dir.exists()}")
        
        # 检查通知器
        notifier = WechatNotifier()
        print(f"微信通知器:")
        print(f"  启用状态: {notifier.enabled}")
        print(f"  Webhook配置: {'已配置' if notifier.webhook_url else '未配置'}")
        
        if not analyzer.output_dir.exists():
            print("❌ 词云输出目录不存在")
            return False
        
        if not notifier.enabled:
            print("❌ 微信通知未启用")
            return False
        
        print("✅ 词云配置正常")
        return True
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试词云发送修复...")
    print("=" * 60)
    
    tests = [
        ("词云配置检查", check_wordcloud_config),
        ("真实数据词云测试", test_wordcloud_with_real_data),
        ("任务管理器词云逻辑测试", test_task_manager_wordcloud_logic)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            print(f"\n📋 测试: {test_name}")
            if test_func():
                passed += 1
                print(f"   ✅ {test_name} - 通过")
            else:
                print(f"   ❌ {test_name} - 失败")
        except Exception as e:
            print(f"   ❌ {test_name} - 执行失败: {e}")
    
    print("\n" + "=" * 60)
    print(f"🎯 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 词云发送功能正常！")
        print("\n✅ 修复总结:")
        print("1. 分析报告现在显示所有群组，不再截断")
        print("2. 词云生成和发送功能正常工作")
        print("3. 定时任务的词云发送逻辑正确")
    else:
        print("⚠️ 部分测试失败，需要进一步调查")
        
        if passed >= 2:
            print("\n💡 可能的原因:")
            print("- 词云生成需要足够的文本内容")
            print("- 任务执行时间较长，需要耐心等待")
            print("- 检查日志中的词云生成和发送记录")
    
    return passed >= 2  # 至少2个测试通过就算成功

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
