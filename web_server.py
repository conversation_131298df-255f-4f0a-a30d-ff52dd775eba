#!/usr/bin/env python3
"""
Web服务器启动脚本

启动基于FastAPI的Web管理界面。
"""

import sys
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

import uvicorn
from web.api_server import create_app
from loguru import logger


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='微信群聊分析系统 Web服务器')
    parser.add_argument('--host', default='0.0.0.0', help='服务器地址')
    parser.add_argument('--port', type=int, default=8000, help='服务器端口')
    parser.add_argument('--reload', action='store_true', help='启用自动重载（开发模式）')
    parser.add_argument('--log-level', default='info', help='日志级别')
    
    args = parser.parse_args()
    
    try:
        logger.info(f"启动Web服务器: http://{args.host}:{args.port}")
        
        # 创建FastAPI应用
        app = create_app()
        
        # 启动服务器
        uvicorn.run(
            app,
            host=args.host,
            port=args.port,
            reload=args.reload,
            log_level=args.log_level
        )
        
    except KeyboardInterrupt:
        logger.info("Web服务器已停止")
    except Exception as e:
        logger.error(f"Web服务器启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
