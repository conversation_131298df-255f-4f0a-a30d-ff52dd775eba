#!/usr/bin/env python3
"""
生成示例分析数据脚本

用于生成包含关键信息的示例分析数据，解决关键信息显示"无"的问题。
"""

import sys
import os
from datetime import datetime, timedelta
import json

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.db_manager import DatabaseManager
from database.models import AnalysisResult


def generate_sample_analysis_data():
    """生成示例分析数据"""
    
    db_manager = DatabaseManager()
    
    # 示例分析数据
    sample_data = [
        {
            'group_id': '7314347876@chatroom',
            'main_topic': 'IC7100、岳师行程及四川成都周边自驾游经历和看法的讨论',
            'topic_category': 'travel',
            'sentiment_score': 0.6,
            'discussion_heat': 8,
            'key_information': [
                'IC7100性能很好',
                'IC7100价格咨询',
                '岳师准备周末进来',
                '四川成都自驾游很好玩',
                '疫情期间自驾游体验很好',
                '不堵车的自驾游是不完美的'
            ]
        },
        {
            'group_id': '49531167254@chatroom',
            'main_topic': '汽车越野性能和配置讨论',
            'topic_category': 'automotive',
            'sentiment_score': 0.3,
            'discussion_heat': 6,
            'key_information': [
                '后悬挂是独立悬挂',
                '比城市SUV强一点',
                '不能算重度越野',
                '带空悬配置',
                '1.5增程动力',
                '内饰比917差但价格便宜一半'
            ]
        },
        {
            'group_id': '21131046013@chatroom',
            'main_topic': '汽车保险购买和价格讨论',
            'topic_category': 'insurance',
            'sentiment_score': -0.1,
            'discussion_heat': 5,
            'key_information': [
                '后杠坏了没报保险',
                '保险价格7300元',
                '今年保险价格变化',
                '保险到期提醒',
                '是否出过险影响价格'
            ]
        },
        {
            'group_id': '43956198728@chatroom',
            'main_topic': '甘南自驾游攻略和高原适应讨论',
            'topic_category': 'travel',
            'sentiment_score': 0.4,
            'discussion_heat': 7,
            'key_information': [
                '甘南自驾攻略七八天',
                '月中青甘环线',
                '最高海拔3800米',
                '建议先在西宁适应',
                '高原反应预防',
                '两天能到达目的地'
            ]
        },
        {
            'group_id': '45349136732@chatroom',
            'main_topic': '股票市场分析和投资策略讨论',
            'topic_category': 'stock',
            'sentiment_score': 0.2,
            'discussion_heat': 6,
            'key_information': [
                '王小雨减仓操作',
                '上证满减策略',
                '满3300减200',
                '满3500减300',
                '投资者没子弹没认知',
                '市场满减剧情分析'
            ]
        }
    ]
    
    print("开始生成示例分析数据...")
    
    for data in sample_data:
        try:
            # 创建分析结果对象
            analysis_result = AnalysisResult(
                group_id=data['group_id'],
                analysis_time=datetime.now(),
                time_window_start=datetime.now() - timedelta(hours=24),
                time_window_end=datetime.now(),
                message_count=50,
                topics=[data['topic_category']],
                main_topic=data['main_topic'],
                topic_category=data['topic_category'],
                sentiment_score=data['sentiment_score'],
                sentiment_label='positive' if data['sentiment_score'] > 0 else 'negative' if data['sentiment_score'] < 0 else 'neutral',
                deep_analysis={},
                overall_score=data['discussion_heat'],
                recommendations=[],
                raw_messages=[],
                ai_response=f"分析了{data['main_topic']}相关的讨论内容",
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            
            # 添加自定义字段
            analysis_result.discussion_heat = data['discussion_heat']
            analysis_result.key_information = data['key_information']
            
            # 保存到数据库
            db_manager.save_analysis_result(analysis_result)
            
            print(f"✅ 已生成群组 {data['group_id']} 的分析数据")
            
        except Exception as e:
            print(f"❌ 生成群组 {data['group_id']} 的分析数据失败: {e}")
    
    print("\n示例分析数据生成完成！")
    
    # 验证数据
    print("\n验证生成的数据:")
    results = db_manager.get_analysis_results(limit=10)
    for result in results:
        print(f"群组: {result.group_id}")
        print(f"主题: {result.main_topic}")
        print(f"讨论热度: {getattr(result, 'discussion_heat', 'N/A')}")
        print(f"关键信息: {getattr(result, 'key_information', [])}")
        print("-" * 50)


if __name__ == "__main__":
    generate_sample_analysis_data()
