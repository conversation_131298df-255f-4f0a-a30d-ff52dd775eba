"""
话题分析器

负责消息话题的识别、分类和分析。
"""

from typing import Dict, Any, List, Optional
from datetime import datetime
from loguru import logger

from .doubao_client import DoubaoClient
from database.models import AnalysisResult


class TopicAnalyzer:
    """话题分析器类"""
    
    def __init__(self):
        """初始化话题分析器"""
        self.doubao_client = DoubaoClient()
        
        # 话题分类映射
        self.category_mapping = {
            '股票': 'stock',
            '时事': 'news', 
            '链接': 'link',
            '哲学感悟': 'philosophy',
            '其他': 'other'
        }
    
    def analyze_topics(self, message_text: str) -> Dict[str, Any]:
        """
        分析消息话题
        
        Args:
            message_text: 消息文本
            
        Returns:
            话题分析结果
        """
        try:
            logger.info("开始话题分析")
            
            # 调用豆包API进行分析
            analysis_result = self.doubao_client.analyze_topic_and_sentiment(message_text)
            
            # 标准化话题分类
            topic_category = analysis_result.get('topic_category', '其他')
            standardized_category = self.category_mapping.get(topic_category, 'other')
            
            # 提取实体信息
            entities = self.doubao_client.extract_entities(message_text)
            
            # 构建结果
            result = {
                'main_topic': analysis_result.get('main_topic', ''),
                'topic_category': standardized_category,
                'original_category': topic_category,
                'topics': analysis_result.get('topics', []),
                'entities': entities,
                'discussion_heat': analysis_result.get('discussion_heat', 5),
                'key_information': analysis_result.get('key_information', []),
                'summary': analysis_result.get('summary', ''),
                'analysis_time': datetime.now().isoformat()
            }
            
            logger.info(f"话题分析完成: 主题={result['main_topic']}, 分类={standardized_category}")
            return result
            
        except Exception as e:
            logger.error(f"话题分析失败: {e}")
            return {
                'main_topic': '分析失败',
                'topic_category': 'other',
                'original_category': '其他',
                'topics': [],
                'entities': {},
                'discussion_heat': 1,
                'key_information': [],
                'summary': f'话题分析失败: {str(e)}',
                'analysis_time': datetime.now().isoformat()
            }
    
    def classify_topic_priority(self, topic_category: str, entities: Dict[str, List[str]]) -> str:
        """
        根据话题分类和实体确定优先级
        
        Args:
            topic_category: 话题分类
            entities: 实体信息
            
        Returns:
            优先级等级 (high/medium/low)
        """
        # 高优先级：股票相关或包含重要实体
        if topic_category == 'stock' or entities.get('stocks'):
            return 'high'
        
        # 高优先级：时事新闻
        if topic_category == 'news':
            return 'high'
        
        # 中优先级：包含链接或公司信息
        if topic_category == 'link' or entities.get('companies') or entities.get('urls'):
            return 'medium'
        
        # 中优先级：哲学感悟
        if topic_category == 'philosophy':
            return 'medium'
        
        # 低优先级：其他
        return 'low'
    
    def extract_stock_mentions(self, entities: Dict[str, List[str]], 
                             key_information: List[str]) -> List[Dict[str, Any]]:
        """
        提取股票相关信息
        
        Args:
            entities: 实体信息
            key_information: 关键信息列表
            
        Returns:
            股票信息列表
        """
        stock_mentions = []
        
        # 从实体中提取股票
        stocks = entities.get('stocks', [])
        companies = entities.get('companies', [])
        
        for stock in stocks:
            stock_info = {
                'symbol': stock,
                'type': 'stock_code',
                'mentioned_in': 'entities'
            }
            stock_mentions.append(stock_info)
        
        for company in companies:
            stock_info = {
                'symbol': company,
                'type': 'company_name', 
                'mentioned_in': 'entities'
            }
            stock_mentions.append(stock_info)
        
        # 从关键信息中查找股票相关内容
        for info in key_information:
            if any(keyword in info.lower() for keyword in ['股票', '股价','mstr', 'mstz','期货', '黄金','T', '大饼','红', '绿','大盘', 'v','拉了', '跳水', '涨跌', '买入', '卖出','煤机', 'soxs', 'A股', '缅A', 'GDXD', '半导体', '达子', '空', '美股', '港股', '特斯拉']):
                stock_info = {
                    'symbol': info,
                    'type': 'stock_related',
                    'mentioned_in': 'key_information'
                }
                stock_mentions.append(stock_info)
        
        return stock_mentions
    
    def extract_news_events(self, entities: Dict[str, List[str]], 
                          key_information: List[str]) -> List[Dict[str, Any]]:
        """
        提取新闻事件信息
        
        Args:
            entities: 实体信息
            key_information: 关键信息列表
            
        Returns:
            新闻事件列表
        """
        news_events = []
        
        # 从实体中提取事件
        events = entities.get('events', [])
        dates = entities.get('dates', [])
        locations = entities.get('locations', [])
        
        for event in events:
            event_info = {
                'event': event,
                'type': 'event',
                'related_dates': dates,
                'related_locations': locations
            }
            news_events.append(event_info)
        
        # 从关键信息中查找新闻相关内容
        for info in key_information:
            if any(keyword in info.lower() for keyword in ['新闻', '消息', '报道', '发布', '宣布', '最新', '今日', '近期', '今天']):
                event_info = {
                    'event': info,
                    'type': 'news_related',
                    'related_dates': [],
                    'related_locations': []
                }
                news_events.append(event_info)
        
        return news_events
    
    def extract_links(self, entities: Dict[str, List[str]]) -> List[Dict[str, str]]:
        """
        提取链接信息
        
        Args:
            entities: 实体信息
            
        Returns:
            链接信息列表
        """
        links = []
        urls = entities.get('urls', [])
        
        for url in urls:
            link_info = {
                'url': url,
                'domain': self._extract_domain(url),
                'type': self._classify_link_type(url)
            }
            links.append(link_info)
        
        return links
    
    def _extract_domain(self, url: str) -> str:
        """从URL中提取域名"""
        try:
            from urllib.parse import urlparse
            parsed = urlparse(url)
            return parsed.netloc
        except:
            return url
    
    def _classify_link_type(self, url: str) -> str:
        """分类链接类型"""
        url_lower = url.lower()
        
        if any(domain in url_lower for domain in ['weibo.com', 'twitter.com', 'x.com']):
            return 'social_media'
        elif any(domain in url_lower for domain in ['news.', 'xinhua', 'people.com']):
            return 'news'
        elif any(domain in url_lower for domain in ['youtube.com', 'bilibili.com']):
            return 'video'
        elif any(domain in url_lower for domain in ['github.com', 'stackoverflow.com']):
            return 'tech'
        else:
            return 'other'
    
    def generate_topic_summary(self, analysis_result: Dict[str, Any]) -> str:
        """
        生成话题摘要
        
        Args:
            analysis_result: 分析结果
            
        Returns:
            话题摘要文本
        """
        main_topic = analysis_result.get('main_topic', '未知话题')
        category = analysis_result.get('original_category', '其他')
        heat = analysis_result.get('discussion_heat', 5)
        
        summary_parts = [f"主要话题：{main_topic}"]
        summary_parts.append(f"分类：{category}")
        summary_parts.append(f"讨论热度：{heat}/10")
        
        # 添加关键信息
        key_info = analysis_result.get('key_information', [])
        if key_info:
            summary_parts.append(f"关键信息：{'; '.join(key_info[:3])}")
        
        # 添加实体信息
        entities = analysis_result.get('entities', {})
        entity_summary = []
        
        if entities.get('stocks'):
            entity_summary.append(f"股票：{', '.join(entities['stocks'][:3])}")
        if entities.get('companies'):
            entity_summary.append(f"公司：{', '.join(entities['companies'][:3])}")
        if entities.get('urls'):
            entity_summary.append(f"链接数：{len(entities['urls'])}")
        
        if entity_summary:
            summary_parts.append(f"涉及实体：{'; '.join(entity_summary)}")
        
        return '\n'.join(summary_parts)
