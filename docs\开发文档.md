# 微信群聊内容智能分析系统开发文档

## 1. 开发环境搭建

### 1.1 系统要求
- 操作系统：Windows 10/11, macOS 10.15+, Ubuntu 18.04+
- Python版本：3.8+
- 内存：至少4GB RAM
- 存储：至少2GB可用空间

### 1.2 环境安装

#### 1.2.1 Python环境
```bash
# 检查Python版本
python --version

# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows
venv\Scripts\activate
# macOS/Linux
source venv/bin/activate
```

#### 1.2.2 依赖安装
```bash
# 安装项目依赖
pip install -r requirements.txt

# 验证安装
python -c "import openai, requests, pandas; print('依赖安装成功')"
```

### 1.3 配置设置

#### 1.3.1 环境变量配置
```bash
# 复制环境变量模板
cp .env.template .env

# 编辑.env文件，填入实际配置
ARK_API_KEY=your_doubao_api_key
WECHAT_WEBHOOK_KEY=your_wechat_webhook_key
WECHAT_DB_PATH=D:/wechat/merge_all.db
```

#### 1.3.2 配置文件说明
```yaml
# config/config.yaml
database:
  wechat_db_path: "D:/wechat/merge_all.db"  # 微信数据库路径
  analysis_db_path: "./data/analysis_results.db"  # 分析结果数据库

doubao_api:
  api_key: "${ARK_API_KEY}"  # 豆包API密钥
  base_url: "https://ark.cn-beijing.volces.com/api/v3/bots"
  model_id: "bot-20250709092323-lmvbj"

wechat_notification:
  webhook_url: "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=${WECHAT_WEBHOOK_KEY}"
```

## 2. 项目结构说明

### 2.1 目录结构
```
WxAnalysis/
├── main.py                 # 主程序入口
├── requirements.txt        # 依赖包列表
├── README.md              # 项目说明
├── .env.template          # 环境变量模板
├── config/                # 配置管理
│   ├── __init__.py
│   ├── config.yaml        # 主配置文件
│   └── config_manager.py  # 配置管理器
├── database/              # 数据库操作
│   ├── __init__.py
│   ├── db_manager.py      # 数据库管理器
│   └── models.py          # 数据模型
├── preprocessing/         # 数据预处理
│   ├── __init__.py
│   ├── data_processor.py  # 数据处理器
│   └── message_filter.py  # 消息过滤器
├── analysis/              # 智能分析
│   ├── __init__.py
│   ├── doubao_client.py   # 豆包API客户端
│   ├── topic_analyzer.py  # 话题分析器
│   └── sentiment_analyzer.py  # 情感分析器
├── deep_analysis/         # 深度分析
│   ├── __init__.py
│   ├── stock_analyzer.py  # 股票分析器
│   ├── news_analyzer.py   # 新闻分析器
│   ├── link_analyzer.py   # 链接分析器
│   └── general_analyzer.py # 通用分析器
├── notification/          # 消息通知
│   ├── __init__.py
│   ├── wechat_notifier.py # 企业微信通知器
│   └── report_formatter.py # 报告格式化器
├── tests/                 # 测试用例
│   ├── __init__.py
│   ├── conftest.py        # pytest配置
│   ├── test_config.py     # 配置测试
│   ├── test_database.py   # 数据库测试
│   ├── test_preprocessing.py # 预处理测试
│   └── test_integration.py # 集成测试
├── docs/                  # 文档
│   ├── 需求分析文档.md
│   ├── 系统设计文档.md
│   ├── API接口文档.md
│   ├── 开发文档.md
│   └── 测试文档.md
├── logs/                  # 日志文件
└── data/                  # 数据文件
```

### 2.2 核心模块说明

#### 2.2.1 配置管理模块 (config/)
**功能**：统一管理系统配置，支持环境变量替换
**关键类**：
- `ConfigManager`: 配置管理器，负责加载和解析配置文件

#### 2.2.2 数据库模块 (database/)
**功能**：数据库连接、数据模型定义、数据访问操作
**关键类**：
- `DatabaseManager`: 数据库管理器
- `MessageData`: 微信消息数据模型
- `AnalysisResult`: 分析结果数据模型

#### 2.2.3 数据预处理模块 (preprocessing/)
**功能**：消息获取、过滤、清洗、格式化
**关键类**：
- `DataProcessor`: 数据处理器，协调整个预处理流程
- `MessageFilter`: 消息过滤器，负责消息的过滤和清洗

#### 2.2.4 智能分析模块 (analysis/)
**功能**：调用大模型API进行内容分析
**关键类**：
- `DoubaoClient`: 豆包API客户端
- `TopicAnalyzer`: 话题分析器
- `SentimentAnalyzer`: 情感分析器

#### 2.2.5 深度分析模块 (deep_analysis/)
**功能**：针对特定话题类型的专门分析
**关键类**：
- `StockAnalyzer`: 股票分析器
- `NewsAnalyzer`: 新闻分析器
- `LinkAnalyzer`: 链接分析器
- `GeneralAnalyzer`: 通用分析器

#### 2.2.6 消息通知模块 (notification/)
**功能**：格式化分析结果并发送通知
**关键类**：
- `WechatNotifier`: 企业微信通知器
- `ReportFormatter`: 报告格式化器

## 3. 关键算法实现

### 3.1 消息过滤算法

#### 3.1.1 系统消息过滤
```python
def _is_system_message(self, message: MessageData) -> bool:
    """检查是否为系统消息"""
    content = message.str_content.strip()
    system_patterns = [
        re.compile(r'^".*"撤回了一条消息$'),
        re.compile(r'^系统消息：'),
        re.compile(r'^您已添加了'),
        # 更多模式...
    ]
    return any(pattern.match(content) for pattern in system_patterns)
```

#### 3.1.2 重复消息去除
```python
def filter_messages(self, messages: List[MessageData]) -> List[MessageData]:
    """过滤消息列表"""
    seen_contents = set()
    filtered_messages = []
    
    for message in messages:
        content_key = f"{message.str_talker}:{message.str_content.strip()}"
        if content_key not in seen_contents:
            seen_contents.add(content_key)
            filtered_messages.append(message)
    
    return filtered_messages
```

### 3.2 话题分析算法

#### 3.2.1 话题分类逻辑
```python
def analyze_topics(self, message_text: str) -> Dict[str, Any]:
    """分析消息话题"""
    # 调用豆包API进行分析
    analysis_result = self.doubao_client.analyze_topic_and_sentiment(message_text)
    
    # 标准化话题分类
    topic_category = analysis_result.get('topic_category', '其他')
    standardized_category = self.category_mapping.get(topic_category, 'other')
    
    # 提取实体信息
    entities = self.doubao_client.extract_entities(message_text)
    
    return {
        'main_topic': analysis_result.get('main_topic', ''),
        'topic_category': standardized_category,
        'entities': entities,
        # 更多字段...
    }
```

#### 3.2.2 优先级评分算法
```python
def _get_priority_score(self, result: Dict[str, Any]) -> float:
    """计算分析结果的优先级评分"""
    score = 0.0
    
    # 基于话题分类的权重
    category_weights = {
        'stock': 3.0,
        'news': 2.5,
        'link': 2.0,
        'philosophy': 1.5,
        'other': 1.0
    }
    
    category = result.get('topic_category', 'other')
    score += category_weights.get(category, 1.0)
    
    # 基于讨论热度
    heat = result.get('discussion_heat', 5)
    score += heat * 0.3
    
    # 基于消息数量
    message_count = result.get('message_count', 0)
    score += min(message_count * 0.1, 2.0)
    
    return score
```

### 3.3 股票分析算法

#### 3.3.1 投资评分计算
```python
def _calculate_investment_score(self, stock_info: Dict[str, Any], 
                              key_information: List[str]) -> Dict[str, Any]:
    """计算投资建议评分"""
    score_factors = []
    
    # 基于市场数据的评分
    change_percent = stock_info.get('change_percent', 0)
    if change_percent > 2:
        score_factors.append(('price_momentum', 2))
    elif change_percent < -2:
        score_factors.append(('price_momentum', -2))
    
    # 基于讨论情感的评分
    sentiment_score = stock_info.get('discussion_sentiment', {}).get('score', 0)
    score_factors.append(('discussion_sentiment', sentiment_score * 3))
    
    # 计算总分并标准化
    total_score = sum(factor[1] for factor in score_factors)
    normalized_score = max(1, min(10, 5 + total_score))
    
    return {
        'score': round(normalized_score, 1),
        'recommendation': self._get_recommendation(normalized_score),
        'factors': score_factors
    }
```

## 4. 配置说明

### 4.1 主要配置项

#### 4.1.1 数据库配置
```yaml
database:
  wechat_db_path: "D:/wechat/merge_all.db"  # 微信数据库路径
  analysis_db_path: "./data/analysis_results.db"  # 分析结果数据库路径
```

#### 4.1.2 API配置
```yaml
doubao_api:
  api_key: "${ARK_API_KEY}"  # 通过环境变量设置
  base_url: "https://ark.cn-beijing.volces.com/api/v3/bots"
  model_id: "bot-20250709092323-lmvbj"
  timeout: 30
  max_retries: 3
```

#### 4.1.3 分析配置
```yaml
analysis:
  time_window_hours: 1  # 分析时间窗口
  message_types: [1]    # 消息类型过滤
  min_message_length: 2 # 最小消息长度
  max_message_length: 1000  # 最大消息长度
```

### 4.2 环境变量说明
```bash
# 必需的环境变量
ARK_API_KEY=豆包API密钥
WECHAT_WEBHOOK_KEY=企业微信机器人密钥

# 可选的环境变量
WECHAT_DB_PATH=微信数据库路径
LOG_LEVEL=日志级别
DEBUG=调试模式开关
```

## 5. 扩展开发指南

### 5.1 添加新的深度分析器

#### 5.1.1 创建分析器类
```python
# deep_analysis/custom_analyzer.py
class CustomAnalyzer:
    """自定义分析器"""
    
    def __init__(self):
        self.enabled = True
    
    def analyze_custom_topic(self, topic_info: Dict[str, Any], 
                           key_information: List[str]) -> Dict[str, Any]:
        """分析自定义话题"""
        # 实现分析逻辑
        return {
            'analysis_type': 'custom',
            'results': {},
            'analysis_time': datetime.now().isoformat()
        }
```

#### 5.1.2 注册分析器
```python
# deep_analysis/__init__.py
from .custom_analyzer import CustomAnalyzer

__all__ = ['StockAnalyzer', 'NewsAnalyzer', 'LinkAnalyzer', 
           'GeneralAnalyzer', 'CustomAnalyzer']
```

#### 5.1.3 集成到主流程
```python
# main.py中的_analyze_group_messages方法
def _analyze_group_messages(self, group_id: str, messages: List) -> Dict[str, Any]:
    # 现有分析逻辑...
    
    # 添加自定义分析
    if topic_result['topic_category'] == 'custom':
        from deep_analysis.custom_analyzer import CustomAnalyzer
        custom_analyzer = CustomAnalyzer()
        deep_analysis['custom_analysis'] = custom_analyzer.analyze_custom_topic(
            topic_result, topic_result.get('key_information', [])
        )
```

### 5.2 添加新的通知渠道

#### 5.2.1 创建通知器类
```python
# notification/email_notifier.py
class EmailNotifier:
    """邮件通知器"""
    
    def __init__(self):
        self.smtp_server = "smtp.example.com"
        self.smtp_port = 587
    
    def send_email_report(self, analysis_results: List[Dict], 
                         summary: Dict) -> bool:
        """发送邮件报告"""
        # 实现邮件发送逻辑
        return True
```

#### 5.2.2 集成到通知流程
```python
# main.py中的run_analysis方法
def run_analysis(self, hours: int = None) -> Dict[str, Any]:
    # 现有分析逻辑...
    
    # 发送通知
    wechat_sent = self.wechat_notifier.send_analysis_report(analysis_results, summary)
    
    # 添加邮件通知
    email_notifier = EmailNotifier()
    email_sent = email_notifier.send_email_report(analysis_results, summary)
    
    return {
        'notification_sent': wechat_sent,
        'email_sent': email_sent,
        # 其他字段...
    }
```

## 6. 调试和故障排除

### 6.1 常见问题

#### 6.1.1 数据库连接问题
```python
# 检查数据库文件是否存在
import os
if not os.path.exists("D:/wechat/merge_all.db"):
    print("微信数据库文件不存在")

# 检查数据库权限
try:
    import sqlite3
    conn = sqlite3.connect("D:/wechat/merge_all.db")
    conn.close()
    print("数据库连接正常")
except Exception as e:
    print(f"数据库连接失败: {e}")
```

#### 6.1.2 API调用问题
```python
# 测试豆包API连接
from analysis.doubao_client import DoubaoClient

client = DoubaoClient()
if client.test_connection():
    print("豆包API连接正常")
else:
    print("豆包API连接失败，请检查API密钥")
```

#### 6.1.3 企业微信通知问题
```python
# 测试企业微信通知
from notification.wechat_notifier import WechatNotifier

notifier = WechatNotifier()
if notifier.test_connection():
    print("企业微信通知正常")
else:
    print("企业微信通知失败，请检查Webhook URL")
```

### 6.2 日志分析
```python
# 查看日志文件
tail -f logs/wx_analysis.log

# 过滤错误日志
grep "ERROR" logs/wx_analysis.log

# 查看特定模块日志
grep "DoubaoClient" logs/wx_analysis.log
```

### 6.3 性能监控
```python
# 添加性能监控代码
import time
from functools import wraps

def monitor_performance(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        logger.info(f"{func.__name__} 执行时间: {end_time - start_time:.2f}秒")
        return result
    return wrapper

# 使用装饰器
@monitor_performance
def analyze_group_messages(self, group_id: str, messages: List):
    # 分析逻辑...
```

## 7. 代码规范

### 7.1 命名规范
- 类名：使用PascalCase，如 `DataProcessor`
- 函数名：使用snake_case，如 `get_recent_messages`
- 变量名：使用snake_case，如 `message_count`
- 常量名：使用UPPER_CASE，如 `MAX_RETRIES`

### 7.2 注释规范
```python
def analyze_topics(self, message_text: str) -> Dict[str, Any]:
    """
    分析消息话题
    
    Args:
        message_text: 格式化的消息文本
        
    Returns:
        包含话题分析结果的字典
        
    Raises:
        APIError: 当豆包API调用失败时
    """
```

### 7.3 错误处理规范
```python
try:
    result = self.api_call()
except APIError as e:
    logger.error(f"API调用失败: {e}")
    raise
except Exception as e:
    logger.error(f"未知错误: {e}")
    return default_result
```
