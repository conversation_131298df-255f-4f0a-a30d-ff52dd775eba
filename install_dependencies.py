#!/usr/bin/env python3
"""
依赖包安装脚本

安装项目所需的所有依赖包。
"""

import subprocess
import sys
from pathlib import Path


def install_package(package):
    """安装单个包"""
    try:
        print(f"正在安装 {package}...")
        result = subprocess.run([sys.executable, "-m", "pip", "install", package], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ {package} 安装成功")
            return True
        else:
            print(f"❌ {package} 安装失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ {package} 安装异常: {e}")
        return False


def main():
    """主函数"""
    print("🚀 开始安装微信群聊分析系统依赖包...")
    
    # 新增的依赖包
    new_packages = [
        "jieba>=0.42.1",
        "wordcloud>=1.9.0", 
        "Pillow>=9.0.0",
        "fastapi>=0.104.0",
        "uvicorn>=0.24.0",
        "python-multipart>=0.0.6",
        "jinja2>=3.1.0"
    ]
    
    success_count = 0
    total_count = len(new_packages)
    
    for package in new_packages:
        if install_package(package):
            success_count += 1
    
    print(f"\n📊 安装完成: {success_count}/{total_count} 个包安装成功")
    
    if success_count == total_count:
        print("🎉 所有依赖包安装成功！")
        print("\n可以运行以下命令测试系统:")
        print("python main.py --test")
        return True
    else:
        print("⚠️ 部分依赖包安装失败，请手动安装失败的包")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
