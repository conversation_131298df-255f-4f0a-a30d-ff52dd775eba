"""
pytest配置文件

提供测试的全局配置和fixture。
"""

import pytest
import tempfile
import shutil
from pathlib import Path


@pytest.fixture(scope="session")
def temp_dir():
    """创建临时目录的fixture"""
    temp_path = tempfile.mkdtemp()
    yield temp_path
    shutil.rmtree(temp_path)


@pytest.fixture
def sample_messages():
    """提供示例消息数据的fixture"""
    from database.models import MessageData
    from datetime import datetime
    
    now = int(datetime.now().timestamp())
    
    return [
        MessageData(
            local_id=1,
            talker_id=1,
            msg_svr_id=1001,
            type=1,
            sub_type=0,
            is_sender=0,
            create_time=now - 3600,
            sequence=1,
            str_talker="test_group_1",
            str_content="这是一条测试消息",
            display_content="这是一条测试消息"
        ),
        MessageData(
            local_id=2,
            talker_id=2,
            msg_svr_id=1002,
            type=1,
            sub_type=0,
            is_sender=0,
            create_time=now - 3000,
            sequence=2,
            str_talker="test_group_1",
            str_content="讨论000001股票",
            display_content="讨论000001股票"
        ),
        MessageData(
            local_id=3,
            talker_id=3,
            msg_svr_id=1003,
            type=1,
            sub_type=0,
            is_sender=0,
            create_time=now - 2400,
            sequence=3,
            str_talker="test_group_2",
            str_content="分享链接 https://example.com",
            display_content="分享链接 https://example.com"
        )
    ]


@pytest.fixture
def mock_config():
    """提供模拟配置的fixture"""
    from unittest.mock import Mock
    
    config = Mock()
    config.get.side_effect = lambda key, default=None: {
        'database.wechat_db_path': './test_wechat.db',
        'database.analysis_db_path': './test_analysis.db',
        'doubao_api.api_key': 'test_key',
        'doubao_api.base_url': 'https://test.api.com',
        'doubao_api.model_id': 'test-model',
        'doubao_api.timeout': 30,
        'doubao_api.max_retries': 3,
        'wechat_notification.webhook_url': 'https://test.webhook.com',
        'analysis.time_window_hours': 1,
        'analysis.message_types': [1],
        'analysis.min_message_length': 2,
        'analysis.max_message_length': 1000,
        'logging.level': 'INFO',
        'logging.file_path': './test.log',
        'logging.format': '{time} | {level} | {message}'
    }.get(key, default)
    
    config.get_database_config.return_value = {
        'wechat_db_path': './test_wechat.db',
        'analysis_db_path': './test_analysis.db'
    }
    
    config.get_doubao_config.return_value = {
        'api_key': 'test_key',
        'base_url': 'https://test.api.com',
        'model_id': 'test-model',
        'timeout': 30,
        'max_retries': 3
    }
    
    config.get_analysis_config.return_value = {
        'time_window_hours': 1,
        'message_types': [1],
        'min_message_length': 2,
        'max_message_length': 1000
    }
    
    config.get_logging_config.return_value = {
        'level': 'INFO',
        'file_path': './test.log',
        'format': '{time} | {level} | {message}'
    }
    
    return config


@pytest.fixture
def mock_doubao_response():
    """提供模拟豆包API响应的fixture"""
    return {
        "main_topic": "测试话题",
        "topic_category": "其他",
        "topics": [
            {
                "topic": "测试讨论",
                "confidence": 0.8,
                "keywords": ["测试", "讨论"]
            }
        ],
        "sentiment": {
            "score": 0.2,
            "label": "积极",
            "explanation": "整体讨论氛围积极"
        },
        "key_information": [
            "这是关键信息1",
            "这是关键信息2"
        ],
        "discussion_heat": 6,
        "summary": "这是一个测试话题的讨论摘要"
    }
