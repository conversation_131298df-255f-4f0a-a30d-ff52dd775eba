# 微信群聊内容智能分析系统API接口文档

## 1. 概述

本文档描述了微信群聊内容智能分析系统的内部API接口和外部API集成规范。

## 2. 内部API接口

### 2.1 数据处理接口

#### 2.1.1 获取群组消息
```python
def get_recent_group_messages(hours: int = 1) -> Dict[str, List[MessageData]]
```

**功能描述**：获取最近指定小时内的群组消息

**参数**：
- `hours` (int): 时间窗口，单位小时，默认1

**返回值**：
```python
{
    "group_id_1": [MessageData, MessageData, ...],
    "group_id_2": [MessageData, MessageData, ...],
    ...
}
```

**异常**：
- `FileNotFoundError`: 微信数据库文件不存在
- `sqlite3.Error`: 数据库访问错误

#### 2.1.2 准备分析数据
```python
def prepare_analysis_data(group_id: str, messages: List[MessageData]) -> Dict[str, Any]
```

**功能描述**：为AI分析准备格式化的数据

**参数**：
- `group_id` (str): 群组ID
- `messages` (List[MessageData]): 消息列表

**返回值**：
```python
{
    "group_id": "群组ID",
    "message_count": 10,
    "messages": [
        {"time": "10:30:00", "content": "消息内容", "sender_id": 123},
        ...
    ],
    "time_range": {
        "start": datetime,
        "end": datetime,
        "duration_minutes": 30.5
    },
    "participants": [123, 456, 789],
    "keywords": [("关键词", 频次), ...],
    "message_intents": {"chat": 5, "question": 2, ...}
}
```

### 2.2 智能分析接口

#### 2.2.1 话题和情感分析
```python
def analyze_topic_and_sentiment(message_text: str) -> Dict[str, Any]
```

**功能描述**：使用豆包API分析消息的话题和情感

**参数**：
- `message_text` (str): 格式化的消息文本

**返回值**：
```python
{
    "main_topic": "主要讨论话题",
    "topic_category": "股票|时事|链接|哲学感悟|其他",
    "topics": [
        {
            "topic": "具体话题",
            "confidence": 0.9,
            "keywords": ["关键词1", "关键词2"]
        }
    ],
    "sentiment": {
        "score": 0.2,
        "label": "积极|消极|中性",
        "explanation": "情感分析说明"
    },
    "key_information": ["关键信息1", "关键信息2"],
    "discussion_heat": 8,
    "summary": "讨论摘要"
}
```

**异常**：
- `APIError`: 豆包API调用失败
- `JSONDecodeError`: 响应解析失败

#### 2.2.2 实体提取
```python
def extract_entities(message_text: str) -> Dict[str, List[str]]
```

**功能描述**：从消息中提取各类实体

**返回值**：
```python
{
    "stocks": ["000001", "SH600000"],
    "companies": ["腾讯", "阿里巴巴"],
    "persons": ["张三", "李四"],
    "locations": ["北京", "上海"],
    "urls": ["https://example.com"],
    "dates": ["2025-07-09"],
    "numbers": ["100万", "5%"],
    "events": ["财报发布", "股东大会"]
}
```

### 2.3 深度分析接口

#### 2.3.1 股票分析
```python
def analyze_stock_discussion(stock_mentions: List[Dict], key_information: List[str]) -> Dict[str, Any]
```

**功能描述**：分析股票相关讨论

**参数**：
- `stock_mentions`: 股票提及信息
- `key_information`: 关键信息列表

**返回值**：
```python
{
    "stocks": [
        {
            "symbol": "000001",
            "type": "stock_code",
            "current_price": 12.34,
            "change_percent": 2.5,
            "discussion_sentiment": {
                "score": 0.3,
                "label": "positive",
                "positive_mentions": 5,
                "negative_mentions": 1
            },
            "investment_score": {
                "score": 7.2,
                "recommendation": "建议关注",
                "factors": [("price_momentum", 2), ("sentiment", 1.5)]
            },
            "risk_assessment": {
                "level": "medium",
                "factors": ["估值偏高"]
            }
        }
    ],
    "overall_analysis": {
        "total_stocks": 3,
        "average_investment_score": 6.8,
        "risk_distribution": {"low": 1, "medium": 2, "high": 0}
    }
}
```

### 2.4 通知接口

#### 2.4.1 发送分析报告
```python
def send_analysis_report(analysis_results: List[Dict], summary: Dict) -> bool
```

**功能描述**：通过企业微信发送分析报告

**参数**：
- `analysis_results`: 分析结果列表
- `summary`: 分析摘要

**返回值**：
- `bool`: 发送是否成功

#### 2.4.2 发送错误通知
```python
def send_error_notification(error_message: str, context: str = "") -> bool
```

**功能描述**：发送错误通知

## 3. 外部API集成

### 3.1 豆包API集成

#### 3.1.1 API配置
```python
# 环境变量
ARK_API_KEY = "your_api_key_here"

# 配置参数
{
    "base_url": "https://ark.cn-beijing.volces.com/api/v3/bots",
    "model_id": "bot-20250709092323-lmvbj",
    "timeout": 30,
    "max_retries": 3
}
```

#### 3.1.2 请求格式
```python
{
    "model": "bot-20250709092323-lmvbj",
    "messages": [
        {
            "role": "system",
            "content": "系统提示词"
        },
        {
            "role": "user", 
            "content": "用户输入"
        }
    ],
    "temperature": 0.7,
    "max_tokens": 2000
}
```

#### 3.1.3 响应格式
```python
{
    "choices": [
        {
            "message": {
                "content": "AI响应内容"
            }
        }
    ],
    "references": []  # 可选的引用信息
}
```

#### 3.1.4 错误处理
```python
# 常见错误码
{
    401: "认证失败",
    429: "请求频率限制",
    500: "服务器内部错误"
}

# 重试策略
- 指数退避：1s, 2s, 4s
- 最大重试次数：3次
- 超时时间：30秒
```

### 3.2 企业微信API集成

#### 3.2.1 Webhook配置
```python
webhook_url = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=YOUR_KEY"
```

#### 3.2.2 消息格式
```python
# 文本消息
{
    "msgtype": "text",
    "text": {
        "content": "消息内容",
        "mentioned_list": ["@all"],  # 可选
        "mentioned_mobile_list": ["13800001111"]  # 可选
    }
}

# Markdown消息
{
    "msgtype": "markdown",
    "markdown": {
        "content": "## 标题\n内容"
    }
}
```

#### 3.2.3 响应格式
```python
# 成功响应
{
    "errcode": 0,
    "errmsg": "ok"
}

# 错误响应
{
    "errcode": 93000,
    "errmsg": "invalid webhook url"
}
```

### 3.3 股票数据API集成 (可扩展)

#### 3.3.1 Tushare API
```python
import tushare as ts

# 配置
ts.set_token('your_token')
pro = ts.pro_api()

# 获取股票基本信息
df = pro.stock_basic(exchange='', list_status='L')

# 获取日线行情
df = pro.daily(ts_code='000001.SZ', start_date='20250701', end_date='20250709')
```

#### 3.3.2 AKShare API
```python
import akshare as ak

# 获取实时行情
stock_zh_a_spot_em_df = ak.stock_zh_a_spot_em()

# 获取个股信息
stock_individual_info_em_df = ak.stock_individual_info_em(symbol="000001")
```

## 4. 数据模型

### 4.1 MessageData模型
```python
@dataclass
class MessageData:
    local_id: int
    talker_id: int
    msg_svr_id: int
    type: int
    sub_type: int
    is_sender: int
    create_time: int
    sequence: int
    str_talker: str
    str_content: str
    display_content: Optional[str] = None
    
    @property
    def create_datetime(self) -> datetime
    
    @property
    def is_text_message(self) -> bool
    
    def to_dict(self) -> Dict[str, Any]
```

### 4.2 AnalysisResult模型
```python
@dataclass
class AnalysisResult:
    id: Optional[int] = None
    analysis_time: Optional[datetime] = None
    group_id: str = ""
    time_window_start: Optional[datetime] = None
    time_window_end: Optional[datetime] = None
    message_count: int = 0
    topics: List[Dict[str, Any]] = None
    main_topic: str = ""
    topic_category: str = ""
    sentiment_score: float = 0.0
    sentiment_label: str = ""
    deep_analysis: Dict[str, Any] = None
    overall_score: float = 0.0
    recommendations: List[str] = None
    raw_messages: List[Dict[str, Any]] = None
    ai_response: str = ""
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    def add_topic(self, topic: str, confidence: float, keywords: List[str])
    def add_recommendation(self, recommendation: str, priority: str)
    def to_dict(self) -> Dict[str, Any]
    def to_json(self) -> str
```

## 5. 错误码定义

### 5.1 系统错误码
```python
ERROR_CODES = {
    1001: "配置文件加载失败",
    1002: "数据库连接失败", 
    1003: "API密钥无效",
    1004: "网络连接超时",
    1005: "数据格式错误",
    2001: "消息过滤失败",
    2002: "数据预处理失败",
    3001: "豆包API调用失败",
    3002: "话题分析失败",
    3003: "情感分析失败",
    4001: "股票分析失败",
    4002: "新闻分析失败",
    4003: "链接解析失败",
    5001: "企业微信通知失败",
    5002: "报告格式化失败"
}
```

### 5.2 HTTP状态码
```python
HTTP_STATUS_CODES = {
    200: "请求成功",
    400: "请求参数错误",
    401: "认证失败",
    403: "权限不足",
    404: "资源不存在",
    429: "请求频率限制",
    500: "服务器内部错误",
    502: "网关错误",
    503: "服务不可用"
}
```

## 6. 接口使用示例

### 6.1 完整分析流程
```python
# 1. 初始化系统
system = WechatAnalysisSystem()

# 2. 运行分析
result = system.run_analysis(hours=1)

# 3. 检查结果
if result['status'] == 'success':
    print(f"分析完成: 处理{result['processed_groups']}个群组")
else:
    print(f"分析失败: {result.get('error')}")
```

### 6.2 单独调用分析接口
```python
# 话题分析
topic_analyzer = TopicAnalyzer()
result = topic_analyzer.analyze_topics("群聊消息文本")

# 情感分析
sentiment_analyzer = SentimentAnalyzer()
result = sentiment_analyzer.analyze_sentiment("群聊消息文本")

# 股票分析
stock_analyzer = StockAnalyzer()
result = stock_analyzer.analyze_stock_discussion(stock_mentions, key_info)
```
