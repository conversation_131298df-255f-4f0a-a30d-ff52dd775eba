"""
配置管理模块测试
"""

import pytest
import os
import tempfile
from pathlib import Path

from config.config_manager import ConfigManager


class TestConfigManager:
    """配置管理器测试类"""
    
    def setup_method(self):
        """测试前准备"""
        # 创建临时配置文件
        self.temp_dir = tempfile.mkdtemp()
        self.config_file = Path(self.temp_dir) / "test_config.yaml"
        
        # 写入测试配置
        test_config = """
database:
  wechat_db_path: "test_wechat.db"
  analysis_db_path: "test_analysis.db"

doubao_api:
  api_key: "${TEST_API_KEY}"
  base_url: "https://test.api.com"
  model_id: "test-model"

analysis:
  time_window_hours: 2
  message_types: [1, 2]
"""
        
        with open(self.config_file, 'w', encoding='utf-8') as f:
            f.write(test_config)
        
        # 设置环境变量
        os.environ['TEST_API_KEY'] = 'test_key_123'
    
    def teardown_method(self):
        """测试后清理"""
        import shutil
        shutil.rmtree(self.temp_dir)
        if 'TEST_API_KEY' in os.environ:
            del os.environ['TEST_API_KEY']
    
    def test_config_loading(self):
        """测试配置加载"""
        config = ConfigManager(str(self.config_file))
        
        assert config.get('database.wechat_db_path') == 'test_wechat.db'
        assert config.get('doubao_api.api_key') == 'test_key_123'  # 环境变量替换
        assert config.get('analysis.time_window_hours') == 2
    
    def test_get_method(self):
        """测试get方法"""
        config = ConfigManager(str(self.config_file))
        
        # 测试存在的键
        assert config.get('database.wechat_db_path') == 'test_wechat.db'
        
        # 测试不存在的键
        assert config.get('nonexistent.key') is None
        assert config.get('nonexistent.key', 'default') == 'default'
    
    def test_specialized_getters(self):
        """测试专门的getter方法"""
        config = ConfigManager(str(self.config_file))
        
        db_config = config.get_database_config()
        assert db_config['wechat_db_path'] == 'test_wechat.db'
        
        doubao_config = config.get_doubao_config()
        assert doubao_config['api_key'] == 'test_key_123'
        
        analysis_config = config.get_analysis_config()
        assert analysis_config['time_window_hours'] == 2
    
    def test_env_variable_replacement(self):
        """测试环境变量替换"""
        config = ConfigManager(str(self.config_file))
        
        # 测试环境变量被正确替换
        assert config.get('doubao_api.api_key') == 'test_key_123'
    
    def test_missing_config_file(self):
        """测试配置文件不存在的情况"""
        with pytest.raises(FileNotFoundError):
            ConfigManager('nonexistent_config.yaml')
