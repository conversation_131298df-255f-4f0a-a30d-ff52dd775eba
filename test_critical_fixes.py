#!/usr/bin/env python3
"""
测试关键修复的脚本

验证以下修复是否正常工作：
1. 群组管理-启用分析和禁用分析数据库落地
2. 微信通知报错修复（AnalysisResult对象转换为字典）
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_group_analysis_database_update():
    """测试群组分析状态数据库更新"""
    print("🧪 测试群组分析状态数据库更新...")
    try:
        from database.db_manager import DatabaseManager
        from database.models import GroupInfo
        
        db_manager = DatabaseManager()
        test_group_id = "test_group_critical_fix"
        
        # 测试1: 设置为启用分析
        print("   测试设置为启用分析...")
        success1 = db_manager.set_group_analysis_status(test_group_id, True)
        status1 = db_manager.get_group_analysis_status(test_group_id)
        
        # 测试2: 设置为禁用分析
        print("   测试设置为禁用分析...")
        success2 = db_manager.set_group_analysis_status(test_group_id, False)
        status2 = db_manager.get_group_analysis_status(test_group_id)
        
        # 测试3: 通过update_group_info方法更新
        print("   测试通过update_group_info更新...")
        group_info = GroupInfo(
            group_id=test_group_id,
            group_name="测试群组",
            need_analysis=True,
            created_at=datetime.now()
        )
        db_manager.update_group_info(group_info)
        status3 = db_manager.get_group_analysis_status(test_group_id)
        
        # 验证结果
        if (success1 and success2 and 
            status1 == True and status2 == False and status3 == True):
            print("✅ 群组分析状态数据库更新正常")
            print(f"   启用状态: {status1}")
            print(f"   禁用状态: {status2}")
            print(f"   更新后状态: {status3}")
            return True
        else:
            print(f"❌ 群组分析状态更新异常")
            print(f"   success1={success1}, status1={status1}")
            print(f"   success2={success2}, status2={status2}")
            print(f"   status3={status3}")
            return False
            
    except Exception as e:
        print(f"❌ 群组分析状态测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_wechat_notification_fix():
    """测试微信通知修复"""
    print("\n🧪 测试微信通知修复...")
    try:
        from notification.wechat_notifier import WechatNotifier
        from database.models import AnalysisResult
        from datetime import datetime, timedelta
        
        # 创建测试用的AnalysisResult对象
        test_result = AnalysisResult(
            group_id="test_group_notification",
            analysis_time=datetime.now(),
            time_window_start=datetime.now() - timedelta(hours=1),
            time_window_end=datetime.now(),
            message_count=10,
            topics=["test"],
            main_topic="测试话题",
            topic_category="test",
            sentiment_score=0.5,
            sentiment_label="positive",
            deep_analysis={},
            overall_score=7.0,
            recommendations=[],
            raw_messages=[],
            ai_response="测试分析结果",
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        # 添加新字段
        test_result.discussion_heat = 8.0
        test_result.key_information = ["测试关键信息1", "测试关键信息2"]
        
        # 测试to_dict方法
        print("   测试AnalysisResult.to_dict()方法...")
        result_dict = test_result.to_dict()
        
        if isinstance(result_dict, dict) and 'group_id' in result_dict:
            print("✅ AnalysisResult.to_dict()方法正常")
            print(f"   字典包含字段: {list(result_dict.keys())[:5]}...")
        else:
            print("❌ AnalysisResult.to_dict()方法异常")
            return False
        
        # 测试微信通知器初始化
        print("   测试微信通知器初始化...")
        notifier = WechatNotifier()
        
        if hasattr(notifier, 'enabled'):
            print(f"✅ 微信通知器初始化成功，enabled状态: {notifier.enabled}")
            
            # 测试发送分析报告（使用字典列表）
            print("   测试发送分析报告...")
            analysis_results_dict = [result_dict]
            summary = {
                'total_groups': 1,
                'total_messages': 10,
                'time_window_hours': 1
            }
            
            # 这应该不会抛出异常
            result = notifier.send_analysis_report(analysis_results_dict, summary)
            
            if notifier.enabled:
                print(f"✅ 发送分析报告方法调用成功，结果: {result}")
            else:
                print("✅ 通知器未启用，方法正确返回True")
            
            return True
        else:
            print("❌ 微信通知器缺少enabled属性")
            return False
            
    except Exception as e:
        print(f"❌ 微信通知测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_analysis_result_conversion():
    """测试分析结果对象转换"""
    print("\n🧪 测试分析结果对象转换...")
    try:
        from database.models import AnalysisResult
        from datetime import datetime, timedelta
        
        # 创建测试用的AnalysisResult对象
        test_result = AnalysisResult(
            group_id="test_conversion",
            analysis_time=datetime.now(),
            time_window_start=datetime.now() - timedelta(hours=1),
            time_window_end=datetime.now(),
            message_count=5,
            topics=["conversion_test"],
            main_topic="转换测试话题",
            topic_category="test",
            sentiment_score=0.3,
            sentiment_label="positive",
            deep_analysis={},
            overall_score=6.0,
            recommendations=[],
            raw_messages=[],
            ai_response="转换测试结果",
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        # 测试列表转换
        analysis_results = [test_result]
        analysis_results_dict = [result.to_dict() for result in analysis_results]
        
        # 验证转换结果
        if (len(analysis_results_dict) == 1 and 
            isinstance(analysis_results_dict[0], dict) and
            analysis_results_dict[0]['group_id'] == 'test_conversion'):
            print("✅ 分析结果对象转换正常")
            print(f"   原对象类型: {type(analysis_results[0])}")
            print(f"   转换后类型: {type(analysis_results_dict[0])}")
            print(f"   包含字段数: {len(analysis_results_dict[0])}")
            return True
        else:
            print("❌ 分析结果对象转换异常")
            return False
            
    except Exception as e:
        print(f"❌ 分析结果转换测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试关键修复...")
    print("=" * 60)
    
    tests = [
        test_group_analysis_database_update,
        test_wechat_notification_fix,
        test_analysis_result_conversion
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ 测试执行失败: {e}")
    
    print("\n" + "=" * 60)
    print(f"🎯 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有关键修复测试通过！")
        print("\n✅ 修复验证:")
        print("   1. 群组分析状态数据库更新 - 正常")
        print("   2. 微信通知AnalysisResult转换 - 正常")
        print("   3. 分析结果对象转换机制 - 正常")
        print("\n🔧 修复内容:")
        print("   • 群组管理启用/禁用分析状态现在正确保存到数据库")
        print("   • 微信通知不再因为AnalysisResult对象类型错误而报错")
        print("   • 分析结果对象正确转换为字典格式用于通知")
    else:
        print("⚠️ 部分测试失败，需要进一步检查")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
