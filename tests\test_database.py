"""
数据库模块测试
"""

import pytest
import tempfile
import sqlite3
from datetime import datetime, timedelta
from pathlib import Path

from database.models import MessageData, AnalysisResult, GroupInfo
from database.db_manager import DatabaseManager


class TestMessageData:
    """消息数据模型测试"""
    
    def test_message_data_creation(self):
        """测试消息数据创建"""
        message = MessageData(
            local_id=1,
            talker_id=123,
            msg_svr_id=456,
            type=1,
            sub_type=0,
            is_sender=0,
            create_time=1752018383,
            sequence=1,
            str_talker="test_group",
            str_content="测试消息",
            display_content="测试消息"
        )
        
        assert message.local_id == 1
        assert message.str_content == "测试消息"
        assert message.is_text_message is True
    
    def test_create_datetime_property(self):
        """测试时间转换属性"""
        # 测试秒级时间戳
        message = MessageData(
            local_id=1, talker_id=1, msg_svr_id=1, type=1, sub_type=0,
            is_sender=0, create_time=1752018383, sequence=1,
            str_talker="test", str_content="test"
        )
        
        dt = message.create_datetime
        assert isinstance(dt, datetime)
        
        # 测试毫秒级时间戳
        message_ms = MessageData(
            local_id=1, talker_id=1, msg_svr_id=1, type=1, sub_type=0,
            is_sender=0, create_time=1752018383000, sequence=1,
            str_talker="test", str_content="test"
        )
        
        dt_ms = message_ms.create_datetime
        assert isinstance(dt_ms, datetime)
    
    def test_to_dict(self):
        """测试转换为字典"""
        message = MessageData(
            local_id=1, talker_id=1, msg_svr_id=1, type=1, sub_type=0,
            is_sender=0, create_time=1752018383, sequence=1,
            str_talker="test", str_content="test"
        )
        
        data = message.to_dict()
        assert isinstance(data, dict)
        assert data['local_id'] == 1
        assert data['str_content'] == "test"
        assert 'create_datetime' in data


class TestAnalysisResult:
    """分析结果模型测试"""
    
    def test_analysis_result_creation(self):
        """测试分析结果创建"""
        result = AnalysisResult(
            group_id="test_group",
            main_topic="测试话题",
            topic_category="other"
        )
        
        assert result.group_id == "test_group"
        assert result.main_topic == "测试话题"
        assert result.topic_category == "other"
        assert isinstance(result.topics, list)
        assert isinstance(result.deep_analysis, dict)
    
    def test_add_topic(self):
        """测试添加话题"""
        result = AnalysisResult(group_id="test")
        result.add_topic("测试话题", 0.9, ["关键词1", "关键词2"])
        
        assert len(result.topics) == 1
        assert result.topics[0]['topic'] == "测试话题"
        assert result.topics[0]['confidence'] == 0.9
    
    def test_add_recommendation(self):
        """测试添加建议"""
        result = AnalysisResult(group_id="test")
        result.add_recommendation("测试建议", "high")
        
        assert len(result.recommendations) == 1
        assert result.recommendations[0]['content'] == "测试建议"
        assert result.recommendations[0]['priority'] == "high"
    
    def test_to_dict_and_from_dict(self):
        """测试字典转换"""
        result = AnalysisResult(
            group_id="test",
            main_topic="测试话题"
        )
        
        # 转换为字典
        data = result.to_dict()
        assert isinstance(data, dict)
        assert data['group_id'] == "test"
        
        # 从字典创建
        new_result = AnalysisResult.from_dict(data)
        assert new_result.group_id == "test"
        assert new_result.main_topic == "测试话题"


class TestDatabaseManager:
    """数据库管理器测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.temp_dir = tempfile.mkdtemp()
        self.wechat_db = Path(self.temp_dir) / "test_wechat.db"
        self.analysis_db = Path(self.temp_dir) / "test_analysis.db"
        
        # 创建测试微信数据库
        self._create_test_wechat_db()
        
        # 模拟配置
        import config.config_manager
        original_get_config = config.config_manager.get_config
        
        def mock_get_config():
            class MockConfig:
                def get(self, key, default=None):
                    config_map = {
                        'database.wechat_db_path': str(self.wechat_db),
                        'database.analysis_db_path': str(self.analysis_db)
                    }
                    return config_map.get(key, default)
            return MockConfig()
        
        config.config_manager.get_config = mock_get_config
        self.original_get_config = original_get_config
    
    def teardown_method(self):
        """测试后清理"""
        import shutil
        import config.config_manager
        
        # 恢复原始配置函数
        config.config_manager.get_config = self.original_get_config
        
        # 清理临时文件
        shutil.rmtree(self.temp_dir)
    
    def _create_test_wechat_db(self):
        """创建测试微信数据库"""
        conn = sqlite3.connect(self.wechat_db)
        cursor = conn.cursor()
        
        # 创建MSG表
        cursor.execute('''
            CREATE TABLE MSG (
                localId INTEGER PRIMARY KEY,
                TalkerId INT,
                MsgSvrID INT,
                Type INT,
                SubType INT,
                IsSender INT,
                CreateTime INT,
                Sequence INT,
                StrTalker TEXT,
                StrContent TEXT,
                DisplayContent TEXT
            )
        ''')
        
        # 插入测试数据
        now = int(datetime.now().timestamp())
        test_messages = [
            (1, 1, 1001, 1, 0, 0, now - 1800, 1, 'test_group_1', '测试消息1', '测试消息1'),
            (2, 2, 1002, 1, 0, 0, now - 1200, 2, 'test_group_1', '测试消息2', '测试消息2'),
            (3, 3, 1003, 1, 0, 0, now - 600, 3, 'test_group_2', '测试消息3', '测试消息3'),
        ]
        
        cursor.executemany('''
            INSERT INTO MSG (localId, TalkerId, MsgSvrID, Type, SubType, IsSender, 
                           CreateTime, Sequence, StrTalker, StrContent, DisplayContent)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', test_messages)
        
        conn.commit()
        conn.close()
    
    def test_database_initialization(self):
        """测试数据库初始化"""
        db_manager = DatabaseManager()
        
        # 检查分析数据库是否创建
        assert self.analysis_db.exists()
        
        # 检查表是否创建
        conn = sqlite3.connect(self.analysis_db)
        cursor = conn.cursor()
        
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        
        assert 'analysis_results' in tables
        assert 'group_info' in tables
        
        conn.close()
    
    def test_get_recent_messages(self):
        """测试获取最近消息"""
        db_manager = DatabaseManager()
        
        # 获取最近1小时的消息
        messages = db_manager.get_recent_messages(hours=1)
        
        assert isinstance(messages, dict)
        # 应该有测试数据
        assert len(messages) > 0
        
        # 检查消息格式
        for group_id, group_messages in messages.items():
            assert isinstance(group_messages, list)
            for message in group_messages:
                assert isinstance(message, MessageData)
                assert message.is_text_message
    
    def test_save_and_get_analysis_result(self):
        """测试保存和获取分析结果"""
        db_manager = DatabaseManager()
        
        # 创建测试分析结果
        result = AnalysisResult(
            group_id="test_group",
            main_topic="测试话题",
            topic_category="other",
            sentiment_score=0.5,
            message_count=10
        )
        
        # 保存结果
        result_id = db_manager.save_analysis_result(result)
        assert isinstance(result_id, int)
        assert result_id > 0
        
        # 获取结果
        results = db_manager.get_analysis_results(group_id="test_group")
        assert len(results) == 1
        assert results[0].group_id == "test_group"
        assert results[0].main_topic == "测试话题"
    
    def test_group_info_operations(self):
        """测试群组信息操作"""
        db_manager = DatabaseManager()
        
        # 创建群组信息
        group_info = GroupInfo(
            group_id="test_group",
            group_name="测试群组",
            member_count=10,
            last_message_time=datetime.now()
        )
        
        # 更新群组信息
        db_manager.update_group_info(group_info)
        
        # 获取活跃群组
        active_groups = db_manager.get_active_groups()
        assert len(active_groups) >= 1
        
        # 查找我们创建的群组
        test_group = next((g for g in active_groups if g.group_id == "test_group"), None)
        assert test_group is not None
        assert test_group.group_name == "测试群组"
        assert test_group.member_count == 10
