"""
豆包API客户端

负责与豆包大模型API的交互。
"""

import os
import json
import time
import hashlib
import re
from typing import Dict, Any, Optional, List
from openai import OpenAI
from loguru import logger

from config import get_config


class DoubaoClient:
    """豆包API客户端类"""
    
    def __init__(self):
        """初始化豆包客户端"""
        self.config = get_config()
        self.doubao_config = self.config.get_doubao_config()
        
        # 初始化OpenAI客户端
        try:
            # 只使用基本参数，避免版本兼容性问题
            self.client = OpenAI(
                base_url=self.doubao_config.get('base_url', 'https://ark.cn-beijing.volces.com/api/v3/bots'),
                api_key=self.doubao_config.get('api_key')
            )
            logger.debug("OpenAI客户端初始化成功")
        except Exception as e:
            logger.error(f"OpenAI客户端初始化失败: {e}")
            raise
        
        self.model_id = self.doubao_config.get('model_id', 'bot-20250709092323-lmvbj')
        self.timeout = self.doubao_config.get('timeout', 30)
        self.max_retries = self.doubao_config.get('max_retries', 3)

        # 添加缓存和优化配置
        self.enable_cache = self.doubao_config.get('enable_cache', True)
        self.cache = {}  # 简单内存缓存
        self.max_cache_size = self.doubao_config.get('max_cache_size', 1000)
        self.max_input_length = self.doubao_config.get('max_input_length', 2000)

        if not self.doubao_config.get('api_key'):
            raise ValueError("豆包API密钥未配置，请检查环境变量 ARK_API_KEY")
    
    def _make_request(self, messages: List[Dict[str, str]], 
                     temperature: float = 0.7,
                     max_tokens: Optional[int] = None) -> str:
        """
        发送请求到豆包API
        
        Args:
            messages: 消息列表
            temperature: 温度参数
            max_tokens: 最大token数
            
        Returns:
            API响应内容
        """
        for attempt in range(self.max_retries):
            try:
                logger.debug(f"发送请求到豆包API (尝试 {attempt + 1}/{self.max_retries})")
                
                completion = self.client.chat.completions.create(
                    model=self.model_id,
                    messages=messages,
                    temperature=temperature,
                    max_tokens=max_tokens,
                    timeout=self.timeout
                )
                
                response_content = completion.choices[0].message.content
                
                # 记录引用信息（如果有）
                if hasattr(completion, "references"):
                    logger.debug(f"API引用信息: {completion.references}")
                
                logger.debug(f"豆包API响应成功，长度: {len(response_content)}")
                return response_content
                
            except Exception as e:
                logger.warning(f"豆包API请求失败 (尝试 {attempt + 1}/{self.max_retries}): {e}")
                
                if attempt < self.max_retries - 1:
                    # 指数退避
                    wait_time = 2 ** attempt
                    logger.info(f"等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)
                else:
                    logger.error(f"豆包API请求最终失败: {e}")
                    raise

    def _preprocess_text(self, text: str) -> str:
        """
        预处理输入文本以减少token使用

        Args:
            text: 原始文本或包含消息数据的字典

        Returns:
            预处理后的文本
        """
        # 智能处理不同类型的输入
        if isinstance(text, dict):
            # 如果是包含raw_messages的数据结构
            if 'raw_messages' in text:
                logger.debug("检测到包含raw_messages的数据结构，提取文本内容")
                text = self._extract_text_from_analysis_data(text)
            else:
                # 其他字典类型，转换为字符串
                logger.warning(f"预处理文本类型异常: {type(text)}, 转换为字符串")
                text = str(text)
        elif not isinstance(text, str):
            if text is None:
                return ""
            logger.warning(f"预处理文本类型异常: {type(text)}, 内容: {text}")
            text = str(text)

        if not text:
            return text

        # 1. 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text.strip())

        # 2. 移除重复的句子（简单去重）
        sentences = text.split('。')
        unique_sentences = []
        seen = set()

        for sentence in sentences:
            # 确保句子是字符串类型
            if not isinstance(sentence, str):
                sentence = str(sentence)
            sentence = sentence.strip()
            if sentence and sentence not in seen:
                seen.add(sentence)
                unique_sentences.append(sentence)

        text = '。'.join(unique_sentences)

        # 3. 限制长度
        if len(text) > self.max_input_length:
            # 保留前面和后面的内容，中间用省略号连接
            half_length = (self.max_input_length - 10) // 2
            text = text[:half_length] + "...(省略)..." + text[-half_length:]

        return text

    def _extract_text_from_analysis_data(self, analysis_data: dict) -> str:
        """
        从分析数据结构中提取文本内容

        Args:
            analysis_data: 包含raw_messages的分析数据

        Returns:
            提取的文本内容
        """
        text_parts = []

        # 提取raw_messages中的文本内容
        raw_messages = analysis_data.get('raw_messages', [])

        for msg in raw_messages:
            # 处理MessageData对象
            if hasattr(msg, 'str_content'):
                content = msg.str_content
            elif isinstance(msg, dict):
                # 处理字典格式的消息
                content = msg.get('str_content') or msg.get('content', '')
            else:
                # 其他格式，转换为字符串
                content = str(msg)

            if content and content.strip():
                text_parts.append(content.strip())

        # 合并所有文本内容
        combined_text = '\n'.join(text_parts)

        logger.debug(f"从分析数据中提取了 {len(text_parts)} 条消息，总长度: {len(combined_text)}")

        return combined_text

    def _get_cache_key(self, method: str, text: str, extra: str = "") -> str:
        """
        生成缓存键

        Args:
            method: 方法名
            text: 输入文本
            extra: 额外参数（如max_length等）

        Returns:
            缓存键
        """
        content_hash = hashlib.md5(text.encode('utf-8')).hexdigest()
        if extra:
            return f"{method}:{extra}:{content_hash}"
        return f"{method}:{content_hash}"

    def _get_from_cache(self, cache_key: str) -> Optional[Any]:
        """从缓存获取结果"""
        if not self.enable_cache:
            return None
        return self.cache.get(cache_key)

    def _save_to_cache(self, cache_key: str, result: Any):
        """保存结果到缓存"""
        if not self.enable_cache:
            return

        # 简单的LRU策略：如果缓存满了，清除一半
        if len(self.cache) >= self.max_cache_size:
            keys_to_remove = list(self.cache.keys())[:self.max_cache_size // 2]
            for key in keys_to_remove:
                del self.cache[key]

        self.cache[cache_key] = result

    def clear_cache(self):
        """清空缓存"""
        self.cache.clear()
        logger.debug("缓存已清空")

    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        return {
            'cache_size': len(self.cache),
            'max_cache_size': self.max_cache_size,
            'cache_enabled': self.enable_cache
        }
    
    def analyze_topic_and_sentiment(self, message_text: str) -> Dict[str, Any]:
        """
        分析消息的话题和情感

        Args:
            message_text: 消息文本

        Returns:
            分析结果字典
        """
        # 预处理文本
        processed_text = self._preprocess_text(message_text)

        # 检查缓存
        cache_key = self._get_cache_key("analyze_topic_and_sentiment", processed_text)
        cached_result = self._get_from_cache(cache_key)
        if cached_result and isinstance(cached_result, dict):
            logger.debug("使用缓存的话题和情感分析结果")
            return cached_result
        system_prompt = """分析群聊消息，返回JSON格式：
{
    "main_topic": "主要话题",
    "topic_category": "股票|时事|链接|哲学感悟|其他",
    "topics": [{"topic": "话题", "confidence": 0.9, "keywords": ["词1", "词2"]}],
    "sentiment": {"score": 0.2, "label": "积极|消极|中性", "explanation": "说明"},
    "key_information": ["关键信息"],
    "discussion_heat": 8,
    "summary": "讨论总结"
}
要求：sentiment.score范围-1到1，discussion_heat范围1-10，只返回JSON。"""

        user_prompt = f"分析：{processed_text}"
        
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]
        
        try:
            response = self._make_request(messages, temperature=0.3)

            # 确保响应是字符串类型
            if not isinstance(response, str):
                logger.warning(f"API响应类型异常: {type(response)}, 内容: {response}")
                response = str(response)

            # 尝试解析JSON响应
            try:
                result = json.loads(response)
                # 保存到缓存
                self._save_to_cache(cache_key, result)
                logger.debug("话题和情感分析完成")
                return result
            except json.JSONDecodeError as e:
                logger.warning(f"JSON解析失败，尝试提取内容: {e}")
                # 如果JSON解析失败，返回基本结果
                return {
                    "main_topic": "解析失败",
                    "topic_category": "其他",
                    "topics": [],
                    "sentiment": {"score": 0.0, "label": "中性", "explanation": "解析失败"},
                    "key_information": [],
                    "discussion_heat": 5,
                    "summary": response[:200] + "..." if len(response) > 200 else response
                }
                
        except Exception as e:
            logger.error(f"话题和情感分析失败: {e}")
            return {
                "main_topic": "分析失败",
                "topic_category": "其他",
                "topics": [],
                "sentiment": {"score": 0.0, "label": "中性", "explanation": "API调用失败"},
                "key_information": [],
                "discussion_heat": 1,
                "summary": f"分析失败: {str(e)}"
            }
    
    def extract_entities(self, message_text: str) -> Dict[str, List[str]]:
        """
        提取消息中的实体

        Args:
            message_text: 消息文本

        Returns:
            实体字典
        """
        # 预处理文本
        processed_text = self._preprocess_text(message_text)

        # 检查缓存
        cache_key = self._get_cache_key("extract_entities", processed_text)
        cached_result = self._get_from_cache(cache_key)
        if cached_result and isinstance(cached_result, dict):
            logger.debug("使用缓存的实体提取结果")
            return cached_result
        system_prompt = """提取实体，返回JSON：
{
    "stocks": ["股票代码/名称"],
    "companies": ["公司名"],
    "persons": ["人名"],
    "locations": ["地点"],
    "urls": ["链接"],
    "dates": ["日期"],
    "numbers": ["数字"],
    "events": ["事件"]
}
不存在则返回空数组，只返回JSON。"""

        user_prompt = f"提取实体：{processed_text}"
        
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]
        
        try:
            response = self._make_request(messages, temperature=0.1)

            # 确保响应是字符串类型
            if not isinstance(response, str):
                logger.warning(f"API响应类型异常: {type(response)}, 内容: {response}")
                response = str(response)

            result = json.loads(response)
            # 保存到缓存
            self._save_to_cache(cache_key, result)
            logger.debug("实体提取完成")
            return result
        except Exception as e:
            logger.warning(f"实体提取失败: {e}")
            return {
                "stocks": [],
                "companies": [],
                "persons": [],
                "locations": [],
                "urls": [],
                "dates": [],
                "numbers": [],
                "events": []
            }
    
    def generate_summary(self, message_text: str, max_length: int = 200) -> str:
        """
        生成消息摘要

        Args:
            message_text: 消息文本
            max_length: 摘要最大长度

        Returns:
            摘要文本
        """
        # 预处理文本
        processed_text = self._preprocess_text(message_text)

        # 检查缓存
        cache_key = self._get_cache_key("generate_summary", processed_text, str(max_length))
        cached_result = self._get_from_cache(cache_key)
        if cached_result and isinstance(cached_result, str):
            logger.debug("使用缓存的摘要生成结果")
            return cached_result
        system_prompt = f"""生成{max_length}字内摘要，突出主要内容和关键信息，客观中性。"""

        user_prompt = f"摘要：{processed_text}"
        
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]
        
        try:
            response = self._make_request(messages, temperature=0.5)

            # 确保响应是字符串类型
            if not isinstance(response, str):
                logger.warning(f"API响应类型异常: {type(response)}, 内容: {response}")
                response = str(response)

            summary = response.strip()

            # 确保摘要长度不超过限制
            if len(summary) > max_length:
                summary = summary[:max_length-3] + "..."

            # 保存到缓存
            self._save_to_cache(cache_key, summary)
            logger.debug(f"摘要生成完成，长度: {len(summary)}")
            return summary
        except Exception as e:
            logger.warning(f"摘要生成失败: {e}")
            return f"摘要生成失败: {str(e)}"
    
    def test_connection(self) -> bool:
        """
        测试API连接
        
        Returns:
            连接是否成功
        """
        try:
            test_messages = [
                {"role": "system", "content": "你是一个测试助手"},
                {"role": "user", "content": "请回复'连接成功'"}
            ]
            
            response = self._make_request(test_messages, temperature=0.1)
            logger.info(f"豆包API连接测试成功: {response}")
            return True
        except Exception as e:
            logger.error(f"豆包API连接测试失败: {e}")
            return False
