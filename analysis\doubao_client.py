"""
豆包API客户端

负责与豆包大模型API的交互。
"""

import os
import json
import time
from typing import Dict, Any, Optional, List
from openai import OpenAI
from loguru import logger

from config import get_config


class DoubaoClient:
    """豆包API客户端类"""
    
    def __init__(self):
        """初始化豆包客户端"""
        self.config = get_config()
        self.doubao_config = self.config.get_doubao_config()
        
        # 初始化OpenAI客户端
        self.client = OpenAI(
            base_url=self.doubao_config.get('base_url', 'https://ark.cn-beijing.volces.com/api/v3/bots'),
            api_key=self.doubao_config.get('api_key')
        )
        
        self.model_id = self.doubao_config.get('model_id', 'bot-20250709092323-lmvbj')
        self.timeout = self.doubao_config.get('timeout', 30)
        self.max_retries = self.doubao_config.get('max_retries', 3)
        
        if not self.doubao_config.get('api_key'):
            raise ValueError("豆包API密钥未配置，请检查环境变量 ARK_API_KEY")
    
    def _make_request(self, messages: List[Dict[str, str]], 
                     temperature: float = 0.7,
                     max_tokens: Optional[int] = None) -> str:
        """
        发送请求到豆包API
        
        Args:
            messages: 消息列表
            temperature: 温度参数
            max_tokens: 最大token数
            
        Returns:
            API响应内容
        """
        for attempt in range(self.max_retries):
            try:
                logger.debug(f"发送请求到豆包API (尝试 {attempt + 1}/{self.max_retries})")
                
                completion = self.client.chat.completions.create(
                    model=self.model_id,
                    messages=messages,
                    temperature=temperature,
                    max_tokens=max_tokens,
                    timeout=self.timeout
                )
                
                response_content = completion.choices[0].message.content
                
                # 记录引用信息（如果有）
                if hasattr(completion, "references"):
                    logger.debug(f"API引用信息: {completion.references}")
                
                logger.debug(f"豆包API响应成功，长度: {len(response_content)}")
                return response_content
                
            except Exception as e:
                logger.warning(f"豆包API请求失败 (尝试 {attempt + 1}/{self.max_retries}): {e}")
                
                if attempt < self.max_retries - 1:
                    # 指数退避
                    wait_time = 2 ** attempt
                    logger.info(f"等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)
                else:
                    logger.error(f"豆包API请求最终失败: {e}")
                    raise
    
    def analyze_topic_and_sentiment(self, message_text: str) -> Dict[str, Any]:
        """
        分析消息的话题和情感
        
        Args:
            message_text: 消息文本
            
        Returns:
            分析结果字典
        """
        system_prompt = """你是一个专业的群聊内容分析助手。请分析给定的群聊消息，并按照以下JSON格式返回结果：

{
    "main_topic": "主要讨论话题的简短描述",
    "topic_category": "话题分类，必须是以下之一：股票、时事、链接、哲学感悟、其他",
    "topics": [
        {
            "topic": "具体话题",
            "confidence": 0.9,
            "keywords": ["关键词1", "关键词2"]
        }
    ],
    "sentiment": {
        "score": 0.2,
        "label": "情感标签：积极、消极、中性",
        "explanation": "情感分析的简要说明"
    },
    "key_information": [
        "提取的关键信息1",
        "提取的关键信息2"
    ],
    "discussion_heat": 8,
    "summary": "对整个讨论的简要总结"
}

分析要求：
1. topic_category必须准确分类
2. sentiment.score范围为-1到1，-1最消极，1最积极
3. discussion_heat范围为1-10，表示讨论热度
4. 提取真正有价值的关键信息
5. 如果涉及股票，请识别股票代码或名称
6. 如果涉及链接，请标注链接内容
7. 返回标准JSON格式，不要包含其他文本"""

        user_prompt = f"请分析以下群聊内容：\n\n{message_text}"
        
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]
        
        try:
            response = self._make_request(messages, temperature=0.3)
            
            # 尝试解析JSON响应
            try:
                result = json.loads(response)
                logger.debug("话题和情感分析完成")
                return result
            except json.JSONDecodeError as e:
                logger.warning(f"JSON解析失败，尝试提取内容: {e}")
                # 如果JSON解析失败，返回基本结果
                return {
                    "main_topic": "解析失败",
                    "topic_category": "其他",
                    "topics": [],
                    "sentiment": {"score": 0.0, "label": "中性", "explanation": "解析失败"},
                    "key_information": [],
                    "discussion_heat": 5,
                    "summary": response[:200] + "..." if len(response) > 200 else response
                }
                
        except Exception as e:
            logger.error(f"话题和情感分析失败: {e}")
            return {
                "main_topic": "分析失败",
                "topic_category": "其他",
                "topics": [],
                "sentiment": {"score": 0.0, "label": "中性", "explanation": "API调用失败"},
                "key_information": [],
                "discussion_heat": 1,
                "summary": f"分析失败: {str(e)}"
            }
    
    def extract_entities(self, message_text: str) -> Dict[str, List[str]]:
        """
        提取消息中的实体
        
        Args:
            message_text: 消息文本
            
        Returns:
            实体字典
        """
        system_prompt = """请从给定的群聊消息中提取以下类型的实体，并以JSON格式返回：

{
    "stocks": ["股票代码或名称"],
    "companies": ["公司名称"],
    "persons": ["人名"],
    "locations": ["地点"],
    "urls": ["网址链接"],
    "dates": ["日期时间"],
    "numbers": ["重要数字"],
    "events": ["事件名称"]
}

提取要求：
1. 只提取确实存在的实体
2. 股票代码格式如：000001、SH600000、AAPL等
3. 公司名称要完整准确
4. 网址要完整
5. 如果某类实体不存在，返回空数组
6. 返回标准JSON格式"""

        user_prompt = f"请提取以下内容中的实体：\n\n{message_text}"
        
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]
        
        try:
            response = self._make_request(messages, temperature=0.1)
            result = json.loads(response)
            logger.debug("实体提取完成")
            return result
        except Exception as e:
            logger.warning(f"实体提取失败: {e}")
            return {
                "stocks": [],
                "companies": [],
                "persons": [],
                "locations": [],
                "urls": [],
                "dates": [],
                "numbers": [],
                "events": []
            }
    
    def generate_summary(self, message_text: str, max_length: int = 200) -> str:
        """
        生成消息摘要
        
        Args:
            message_text: 消息文本
            max_length: 摘要最大长度
            
        Returns:
            摘要文本
        """
        system_prompt = f"""请为给定的群聊消息生成一个简洁的摘要，要求：

1. 摘要长度不超过{max_length}字
2. 突出主要讨论内容和关键信息
3. 保持客观中性的语调
4. 如果涉及股票、时事等重要话题，要重点体现
5. 直接返回摘要内容，不要包含其他格式"""

        user_prompt = f"请为以下群聊内容生成摘要：\n\n{message_text}"
        
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]
        
        try:
            response = self._make_request(messages, temperature=0.5)
            summary = response.strip()
            
            # 确保摘要长度不超过限制
            if len(summary) > max_length:
                summary = summary[:max_length-3] + "..."
            
            logger.debug(f"摘要生成完成，长度: {len(summary)}")
            return summary
        except Exception as e:
            logger.warning(f"摘要生成失败: {e}")
            return f"摘要生成失败: {str(e)}"
    
    def test_connection(self) -> bool:
        """
        测试API连接
        
        Returns:
            连接是否成功
        """
        try:
            test_messages = [
                {"role": "system", "content": "你是一个测试助手"},
                {"role": "user", "content": "请回复'连接成功'"}
            ]
            
            response = self._make_request(test_messages, temperature=0.1)
            logger.info(f"豆包API连接测试成功: {response}")
            return True
        except Exception as e:
            logger.error(f"豆包API连接测试失败: {e}")
            return False
