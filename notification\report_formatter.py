"""
报告格式化器

负责将分析结果格式化为不同格式的报告。
"""

import json
from typing import Dict, Any, List, Optional
from datetime import datetime
from loguru import logger


class ReportFormatter:
    """报告格式化器类"""
    
    def __init__(self):
        """初始化报告格式化器"""
        pass
    
    def format_detailed_report(self, analysis_results: List[Dict[str, Any]], 
                             summary: Dict[str, Any]) -> str:
        """
        格式化详细报告
        
        Args:
            analysis_results: 分析结果列表
            summary: 分析摘要
            
        Returns:
            详细报告文本
        """
        report_lines = []
        
        # 报告头部
        report_lines.append("=" * 60)
        report_lines.append("微信群聊内容智能分析报告")
        report_lines.append("=" * 60)
        report_lines.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append("")
        
        # 执行摘要
        report_lines.append("📊 执行摘要")
        report_lines.append("-" * 30)
        report_lines.append(f"分析时间窗口: {summary.get('time_window_hours', 1)} 小时")
        report_lines.append(f"处理群组数量: {summary.get('total_groups', 0)}")
        report_lines.append(f"处理消息总数: {summary.get('total_messages', 0)}")
        
        if summary.get('most_active_groups'):
            most_active = summary['most_active_groups'][0]
            report_lines.append(f"最活跃群组: {most_active['group_id'][:30]}... ({most_active['message_count']}条消息)")
        
        report_lines.append("")
        
        # 话题分布统计
        topic_stats = self._calculate_topic_statistics(analysis_results)
        if topic_stats:
            report_lines.append("📈 话题分布统计")
            report_lines.append("-" * 30)
            for category, count in topic_stats.items():
                category_name = self._get_category_name(category)
                percentage = (count / len(analysis_results)) * 100 if analysis_results else 0
                report_lines.append(f"{category_name}: {count} ({percentage:.1f}%)")
            report_lines.append("")
        
        # 情感分析统计
        sentiment_stats = self._calculate_sentiment_statistics(analysis_results)
        if sentiment_stats:
            report_lines.append("😊 情感分析统计")
            report_lines.append("-" * 30)
            report_lines.append(f"平均情感评分: {sentiment_stats['average_score']:.3f}")
            report_lines.append(f"积极讨论: {sentiment_stats['positive_count']} 个群组")
            report_lines.append(f"消极讨论: {sentiment_stats['negative_count']} 个群组")
            report_lines.append(f"中性讨论: {sentiment_stats['neutral_count']} 个群组")
            report_lines.append("")
        
        # 详细分析结果
        if analysis_results:
            report_lines.append("🔍 详细分析结果")
            report_lines.append("-" * 30)
            
            for i, result in enumerate(analysis_results, 1):
                report_lines.append(f"\n{i}. 群组: {result.get('group_id', '未知')}")
                report_lines.append(f"   消息数量: {result.get('message_count', 0)}")
                report_lines.append(f"   主要话题: {result.get('main_topic', '未知')}")
                report_lines.append(f"   话题分类: {self._get_category_name(result.get('topic_category', 'other'))}")
                report_lines.append(f"   情感评分: {result.get('sentiment_score', 0):.3f}")
                report_lines.append(f"   讨论热度: {result.get('discussion_heat', 5)}/10")
                
                # 话题详情（按置信度排序）
                topics = result.get('topics', [])
                if topics:
                    sorted_topics = sorted(topics, key=lambda x: x.get('confidence', 0), reverse=True)
                    report_lines.append("   话题详情:")
                    for topic in sorted_topics[:3]:
                        topic_name = topic.get('topic', '未知话题')
                        confidence = topic.get('confidence', 0)
                        keywords = topic.get('keywords', [])
                        keyword_str = f"关键词: {', '.join(keywords[:5])}" if keywords else "无关键词"
                        report_lines.append(f"     • {topic_name} (置信度: {confidence:.2f}) - {keyword_str}")

                # 关键信息（增加显示数量）
                key_info = result.get('key_information', [])
                if key_info:
                    report_lines.append("   关键信息:")
                    for info in key_info[:5]:  # 增加到5条
                        report_lines.append(f"     • {info}")

                # 聊天摘要（新增）
                chat_summary = result.get('chat_summary', [])
                if chat_summary:
                    report_lines.append("   聊天摘要:")
                    for summary in chat_summary[:3]:
                        report_lines.append(f"     • {summary}")

                # 用户参与情况（新增）
                user_stats = result.get('user_statistics', {})
                if user_stats:
                    active_users = user_stats.get('active_users', 0)
                    total_messages = user_stats.get('total_messages', 0)
                    report_lines.append(f"   参与情况: {active_users}人参与，共{total_messages}条消息")
                
                # 深度分析
                deep_analysis = result.get('deep_analysis', {})
                if deep_analysis:
                    self._add_deep_analysis_details(report_lines, deep_analysis)
                
                # 建议
                recommendations = result.get('recommendations', [])
                if recommendations:
                    report_lines.append("   建议:")
                    for rec in recommendations[:2]:
                        if isinstance(rec, dict):
                            report_lines.append(f"     • {rec.get('content', rec)}")
                        else:
                            report_lines.append(f"     • {rec}")
        
        # 总体建议
        overall_recommendations = self._generate_overall_recommendations(analysis_results, summary)
        if overall_recommendations:
            report_lines.append("\n💡 总体建议")
            report_lines.append("-" * 30)
            for rec in overall_recommendations:
                report_lines.append(f"• {rec}")
        
        # 报告尾部
        report_lines.append("\n" + "=" * 60)
        report_lines.append("报告结束")
        report_lines.append("=" * 60)
        
        return "\n".join(report_lines)
    
    def format_json_report(self, analysis_results: List[Dict[str, Any]], 
                          summary: Dict[str, Any]) -> str:
        """
        格式化JSON报告
        
        Args:
            analysis_results: 分析结果列表
            summary: 分析摘要
            
        Returns:
            JSON格式报告
        """
        report_data = {
            'metadata': {
                'generated_at': datetime.now().isoformat(),
                'report_type': 'wechat_group_analysis',
                'version': '1.0.0'
            },
            'summary': summary,
            'statistics': {
                'topic_distribution': self._calculate_topic_statistics(analysis_results),
                'sentiment_statistics': self._calculate_sentiment_statistics(analysis_results)
            },
            'analysis_results': analysis_results,
            'recommendations': self._generate_overall_recommendations(analysis_results, summary)
        }
        
        return json.dumps(report_data, ensure_ascii=False, indent=2)
    
    def format_markdown_report(self, analysis_results: List[Dict[str, Any]], 
                             summary: Dict[str, Any]) -> str:
        """
        格式化Markdown报告
        
        Args:
            analysis_results: 分析结果列表
            summary: 分析摘要
            
        Returns:
            Markdown格式报告
        """
        report_lines = []
        
        # 标题
        report_lines.append("# 微信群聊内容智能分析报告")
        report_lines.append("")
        report_lines.append(f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append("")
        
        # 执行摘要
        report_lines.append("## 📊 执行摘要")
        report_lines.append("")
        report_lines.append(f"- **分析时间窗口**: {summary.get('time_window_hours', 1)} 小时")
        report_lines.append(f"- **处理群组数量**: {summary.get('total_groups', 0)}")
        report_lines.append(f"- **处理消息总数**: {summary.get('total_messages', 0)}")
        report_lines.append("")
        
        # 话题分布
        topic_stats = self._calculate_topic_statistics(analysis_results)
        if topic_stats:
            report_lines.append("## 📈 话题分布")
            report_lines.append("")
            report_lines.append("| 话题分类 | 数量 | 占比 |")
            report_lines.append("|---------|------|------|")
            
            for category, count in topic_stats.items():
                category_name = self._get_category_name(category)
                percentage = (count / len(analysis_results)) * 100 if analysis_results else 0
                report_lines.append(f"| {category_name} | {count} | {percentage:.1f}% |")
            
            report_lines.append("")
        
        # 详细结果
        if analysis_results:
            report_lines.append("## 🔍 详细分析结果")
            report_lines.append("")
            
            for i, result in enumerate(analysis_results, 1):
                report_lines.append(f"### {i}. 群组分析")
                report_lines.append("")
                report_lines.append(f"- **群组ID**: `{result.get('group_id', '未知')}`")
                report_lines.append(f"- **消息数量**: {result.get('message_count', 0)}")
                report_lines.append(f"- **主要话题**: {result.get('main_topic', '未知')}")
                report_lines.append(f"- **话题分类**: {self._get_category_name(result.get('topic_category', 'other'))}")
                report_lines.append(f"- **情感评分**: {result.get('sentiment_score', 0):.3f}")
                report_lines.append(f"- **讨论热度**: {result.get('discussion_heat', 5)}/10")
                report_lines.append("")
                
                # 关键信息
                key_info = result.get('key_information', [])
                if key_info:
                    report_lines.append("**关键信息**:")
                    for info in key_info[:3]:
                        report_lines.append(f"- {info}")
                    report_lines.append("")
        
        return "\n".join(report_lines)
    
    def _calculate_topic_statistics(self, analysis_results: List[Dict[str, Any]]) -> Dict[str, int]:
        """计算话题统计"""
        stats = {}
        for result in analysis_results:
            category = result.get('topic_category', 'other')
            stats[category] = stats.get(category, 0) + 1
        return stats
    
    def _calculate_sentiment_statistics(self, analysis_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """计算情感统计"""
        if not analysis_results:
            return {}
        
        scores = [result.get('sentiment_score', 0) for result in analysis_results]
        average_score = sum(scores) / len(scores)
        
        positive_count = sum(1 for score in scores if score > 0.2)
        negative_count = sum(1 for score in scores if score < -0.2)
        neutral_count = len(scores) - positive_count - negative_count
        
        return {
            'average_score': average_score,
            'positive_count': positive_count,
            'negative_count': negative_count,
            'neutral_count': neutral_count
        }
    
    def _get_category_name(self, category: str) -> str:
        """获取话题分类的中文名称"""
        name_map = {
            'stock': '股票讨论',
            'news': '时事新闻',
            'link': '链接分享',
            'philosophy': '哲学感悟',
            'other': '其他话题'
        }
        return name_map.get(category, '未知分类')
    
    def _add_deep_analysis_details(self, report_lines: List[str], 
                                 deep_analysis: Dict[str, Any]):
        """添加深度分析详情"""
        if 'stock_analysis' in deep_analysis:
            stock_data = deep_analysis['stock_analysis']
            if stock_data.get('stocks'):
                report_lines.append("   股票分析:")
                for stock in stock_data['stocks'][:3]:
                    symbol = stock.get('symbol', '未知')
                    score = stock.get('investment_score', {}).get('score', 0)
                    report_lines.append(f"     • {symbol}: 投资评分 {score}/10")
    
    def _generate_overall_recommendations(self, analysis_results: List[Dict[str, Any]], 
                                        summary: Dict[str, Any]) -> List[str]:
        """生成总体建议"""
        recommendations = []
        
        if not analysis_results:
            recommendations.append("当前时间窗口内无活跃讨论，建议关注群组活跃度")
            return recommendations
        
        # 基于话题分布的建议
        topic_stats = self._calculate_topic_statistics(analysis_results)
        
        if topic_stats.get('stock', 0) > 0:
            recommendations.append("发现股票相关讨论，建议关注市场动态和投资风险")
        
        if topic_stats.get('news', 0) > 0:
            recommendations.append("发现时事新闻讨论，建议关注事件发展和潜在影响")
        
        # 基于情感分析的建议
        sentiment_stats = self._calculate_sentiment_statistics(analysis_results)
        if sentiment_stats:
            if sentiment_stats['negative_count'] > sentiment_stats['positive_count']:
                recommendations.append("整体讨论情感偏消极，建议关注群组氛围和成员情绪")
            elif sentiment_stats['positive_count'] > len(analysis_results) * 0.7:
                recommendations.append("群组讨论氛围积极，可以继续保持良好的互动环境")
        
        # 基于活跃度的建议
        total_messages = summary.get('total_messages', 0)
        if total_messages > 100:
            recommendations.append("群组讨论非常活跃，建议定期进行内容分析和管理")
        elif total_messages < 10:
            recommendations.append("群组活跃度较低，可以考虑增加互动话题或活动")
        
        return recommendations
