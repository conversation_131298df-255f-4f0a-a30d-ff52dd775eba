# 微信群聊内容智能分析系统测试文档

## 1. 测试概述

### 1.1 测试目标
- 验证系统各模块功能的正确性
- 确保系统集成的稳定性
- 验证系统性能满足需求
- 确保错误处理机制的有效性

### 1.2 测试范围
- 单元测试：各模块的独立功能测试
- 集成测试：模块间协作的测试
- 性能测试：系统性能指标验证
- 错误处理测试：异常情况的处理验证

### 1.3 测试环境
- Python 3.8+
- pytest测试框架
- 模拟数据和API响应
- 临时数据库环境

## 2. 测试策略

### 2.1 测试分层

```
┌─────────────────────────────────────┐
│           集成测试                   │
│  ┌─────────────────────────────────┐ │
│  │          接口测试                │ │
│  │  ┌─────────────────────────────┐ │ │
│  │  │        单元测试              │ │ │
│  │  └─────────────────────────────┘ │ │
│  └─────────────────────────────────┘ │
└─────────────────────────────────────┘
```

### 2.2 测试类型

#### 2.2.1 单元测试
- **目标**：测试各个类和函数的独立功能
- **覆盖范围**：所有公共方法和关键私有方法
- **工具**：pytest + unittest.mock

#### 2.2.2 集成测试
- **目标**：测试模块间的协作和数据流
- **覆盖范围**：完整的业务流程
- **工具**：pytest + 真实数据库

#### 2.2.3 性能测试
- **目标**：验证系统性能指标
- **覆盖范围**：关键操作的执行时间
- **工具**：pytest-benchmark

#### 2.2.4 错误处理测试
- **目标**：验证异常情况的处理
- **覆盖范围**：各种错误场景
- **工具**：pytest + mock异常

## 3. 单元测试

### 3.1 配置管理模块测试

#### 3.1.1 测试用例
```python
class TestConfigManager:
    def test_config_loading(self):
        """测试配置加载"""
        # 测试正常配置加载
        # 测试环境变量替换
        # 测试默认值处理
    
    def test_get_method(self):
        """测试get方法"""
        # 测试存在的键
        # 测试不存在的键
        # 测试默认值返回
    
    def test_missing_config_file(self):
        """测试配置文件不存在的情况"""
        # 验证抛出FileNotFoundError
```

#### 3.1.2 测试覆盖率
- 配置文件加载：100%
- 环境变量替换：100%
- 错误处理：100%

### 3.2 数据库模块测试

#### 3.2.1 测试用例
```python
class TestDatabaseManager:
    def test_database_initialization(self):
        """测试数据库初始化"""
        # 验证表创建
        # 验证索引创建
    
    def test_get_recent_messages(self):
        """测试获取最近消息"""
        # 测试时间窗口过滤
        # 测试消息类型过滤
        # 测试分组功能
    
    def test_save_and_get_analysis_result(self):
        """测试保存和获取分析结果"""
        # 测试数据保存
        # 测试数据查询
        # 测试数据完整性
```

#### 3.2.2 测试数据
```python
# 测试消息数据
test_messages = [
    (1, 1, 1001, 1, 0, 0, now - 1800, 1, 'test_group_1', '测试消息1'),
    (2, 2, 1002, 1, 0, 0, now - 1200, 2, 'test_group_1', '测试消息2'),
    (3, 3, 1003, 1, 0, 0, now - 600, 3, 'test_group_2', '测试消息3'),
]
```

### 3.3 数据预处理模块测试

#### 3.3.1 消息过滤测试
```python
class TestMessageFilter:
    def test_filter_by_type(self):
        """测试按类型过滤"""
        # 验证只保留文本消息
    
    def test_filter_by_length(self):
        """测试按长度过滤"""
        # 验证长度范围过滤
    
    def test_filter_system_messages(self):
        """测试过滤系统消息"""
        # 验证系统消息被过滤
    
    def test_remove_duplicates(self):
        """测试去重"""
        # 验证重复消息被去除
```

#### 3.3.2 数据处理测试
```python
class TestDataProcessor:
    def test_prepare_analysis_data(self):
        """测试准备分析数据"""
        # 验证数据格式化
        # 验证统计信息计算
    
    def test_format_messages_for_ai(self):
        """测试格式化消息给AI"""
        # 验证文本格式
        # 验证上下文信息
```

### 3.4 智能分析模块测试

#### 3.4.1 豆包客户端测试
```python
class TestDoubaoClient:
    @patch('openai.OpenAI')
    def test_analyze_topic_and_sentiment(self, mock_openai):
        """测试话题和情感分析"""
        # 模拟API响应
        # 验证请求格式
        # 验证响应解析
    
    def test_api_error_handling(self):
        """测试API错误处理"""
        # 测试网络错误
        # 测试API限制
        # 测试重试机制
```

#### 3.4.2 话题分析测试
```python
class TestTopicAnalyzer:
    def test_analyze_topics(self):
        """测试话题分析"""
        # 验证话题分类
        # 验证实体提取
        # 验证优先级计算
    
    def test_extract_stock_mentions(self):
        """测试股票提及提取"""
        # 验证股票代码识别
        # 验证公司名称识别
```

## 4. 集成测试

### 4.1 完整流程测试

#### 4.1.1 端到端测试
```python
class TestWechatAnalysisSystemIntegration:
    def test_complete_analysis_workflow(self):
        """测试完整分析工作流"""
        # 1. 数据获取
        # 2. 数据预处理
        # 3. 智能分析
        # 4. 深度分析
        # 5. 结果存储
        # 6. 消息通知
```

#### 4.1.2 测试场景
```python
# 测试场景1：股票讨论分析
test_stock_messages = [
    "今天000001涨了5%",
    "看好这只股票的后续表现",
    "建议大家关注一下"
]

# 测试场景2：时事新闻分析
test_news_messages = [
    "看到一个重要新闻",
    "这个事件可能影响市场",
    "大家怎么看？"
]

# 测试场景3：链接分享分析
test_link_messages = [
    "分享一个有趣的文章 https://example.com",
    "这个链接很有价值",
    "推荐大家看看"
]
```

### 4.2 模块协作测试

#### 4.2.1 数据流测试
```python
def test_data_flow_integrity():
    """测试数据流完整性"""
    # 验证数据在各模块间的传递
    # 验证数据格式的一致性
    # 验证数据的完整性
```

#### 4.2.2 错误传播测试
```python
def test_error_propagation():
    """测试错误传播"""
    # 测试上游错误的处理
    # 测试错误恢复机制
    # 测试降级处理
```

## 5. 性能测试

### 5.1 性能指标

#### 5.1.1 响应时间指标
- 单次完整分析：< 5分钟
- 数据库查询：< 3秒
- API调用：< 30秒
- 消息过滤：< 10秒

#### 5.1.2 吞吐量指标
- 消息处理能力：1000条/次
- 并发API调用：5个/秒
- 数据库写入：100条/秒

### 5.2 性能测试用例

#### 5.2.1 负载测试
```python
@pytest.mark.performance
def test_large_message_processing():
    """测试大量消息处理性能"""
    # 生成1000条测试消息
    messages = generate_test_messages(1000)
    
    start_time = time.time()
    result = processor.process_messages(messages)
    end_time = time.time()
    
    # 验证处理时间 < 60秒
    assert end_time - start_time < 60
    assert len(result) > 0
```

#### 5.2.2 并发测试
```python
@pytest.mark.performance
def test_concurrent_analysis():
    """测试并发分析性能"""
    import concurrent.futures
    
    with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
        futures = []
        for i in range(5):
            future = executor.submit(run_analysis, test_data[i])
            futures.append(future)
        
        results = [future.result() for future in futures]
        assert all(result['status'] == 'success' for result in results)
```

### 5.3 内存使用测试

#### 5.3.1 内存泄漏测试
```python
@pytest.mark.performance
def test_memory_usage():
    """测试内存使用"""
    import psutil
    import gc
    
    process = psutil.Process()
    initial_memory = process.memory_info().rss
    
    # 运行多次分析
    for i in range(10):
        run_analysis()
        gc.collect()
    
    final_memory = process.memory_info().rss
    memory_increase = final_memory - initial_memory
    
    # 验证内存增长 < 100MB
    assert memory_increase < 100 * 1024 * 1024
```

## 6. 错误处理测试

### 6.1 异常场景测试

#### 6.1.1 数据库异常测试
```python
def test_database_connection_error():
    """测试数据库连接错误"""
    with patch('sqlite3.connect') as mock_connect:
        mock_connect.side_effect = sqlite3.Error("连接失败")
        
        with pytest.raises(sqlite3.Error):
            db_manager = DatabaseManager()
```

#### 6.1.2 API异常测试
```python
def test_api_timeout_error():
    """测试API超时错误"""
    with patch('openai.OpenAI.chat.completions.create') as mock_api:
        mock_api.side_effect = TimeoutError("请求超时")
        
        client = DoubaoClient()
        result = client.analyze_topic_and_sentiment("测试文本")
        
        # 验证返回默认结果
        assert result['main_topic'] == '分析失败'
```

#### 6.1.3 网络异常测试
```python
def test_network_error():
    """测试网络错误"""
    with patch('requests.post') as mock_post:
        mock_post.side_effect = requests.ConnectionError("网络连接失败")
        
        notifier = WechatNotifier()
        result = notifier.send_analysis_report([], {})
        
        # 验证返回False
        assert result is False
```

### 6.2 恢复机制测试

#### 6.2.1 重试机制测试
```python
def test_retry_mechanism():
    """测试重试机制"""
    with patch('openai.OpenAI.chat.completions.create') as mock_api:
        # 前两次失败，第三次成功
        mock_api.side_effect = [
            Exception("第一次失败"),
            Exception("第二次失败"),
            Mock(choices=[Mock(message=Mock(content="成功响应"))])
        ]
        
        client = DoubaoClient()
        result = client._make_request([{"role": "user", "content": "测试"}])
        
        # 验证重试3次后成功
        assert mock_api.call_count == 3
        assert result == "成功响应"
```

#### 6.2.2 降级处理测试
```python
def test_fallback_mechanism():
    """测试降级处理"""
    with patch('analysis.doubao_client.DoubaoClient._make_request') as mock_request:
        mock_request.side_effect = Exception("API不可用")
        
        system = WechatAnalysisSystem()
        result = system.run_analysis()
        
        # 验证系统能够处理API失败
        assert result['status'] in ['success', 'error']
```

## 7. 测试执行

### 7.1 测试命令

#### 7.1.1 运行所有测试
```bash
# 运行所有测试
pytest tests/ -v

# 运行特定模块测试
pytest tests/test_database.py -v

# 运行特定测试类
pytest tests/test_database.py::TestDatabaseManager -v

# 运行特定测试方法
pytest tests/test_database.py::TestDatabaseManager::test_get_recent_messages -v
```

#### 7.1.2 生成测试报告
```bash
# 生成覆盖率报告
pytest --cov=. tests/ --cov-report=html

# 生成详细报告
pytest tests/ --html=reports/report.html --self-contained-html

# 运行性能测试
pytest -m performance tests/ -v
```

### 7.2 持续集成

#### 7.2.1 GitHub Actions配置
```yaml
name: Tests
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: [3.8, 3.9, 3.10]
    
    steps:
    - uses: actions/checkout@v2
    - name: Set up Python
      uses: actions/setup-python@v2
      with:
        python-version: ${{ matrix.python-version }}
    
    - name: Install dependencies
      run: |
        pip install -r requirements.txt
        pip install pytest pytest-cov
    
    - name: Run tests
      run: pytest --cov=. tests/
```

## 8. 测试覆盖率

### 8.1 覆盖率目标
- 整体代码覆盖率：≥ 80%
- 核心模块覆盖率：≥ 90%
- 关键函数覆盖率：100%

### 8.2 覆盖率报告

#### 8.2.1 模块覆盖率
```
模块                    覆盖率
config/                 95%
database/               92%
preprocessing/          88%
analysis/               85%
deep_analysis/          75%
notification/           90%
main.py                 80%
```

#### 8.2.2 未覆盖代码分析
- 错误处理分支：部分异常情况难以模拟
- 外部API调用：依赖真实服务的代码
- 配置相关：环境特定的配置逻辑

## 9. 测试维护

### 9.1 测试数据管理
- 使用fixture提供可重用的测试数据
- 定期更新测试数据以反映真实场景
- 保持测试数据的多样性和代表性

### 9.2 测试用例维护
- 定期审查和更新测试用例
- 添加新功能的测试用例
- 修复失效的测试用例

### 9.3 性能基准维护
- 定期更新性能基准
- 监控性能回归
- 优化慢速测试用例

## 10. 测试最佳实践

### 10.1 编写原则
- 测试应该独立且可重复
- 测试名称应该清晰描述测试内容
- 使用AAA模式：Arrange, Act, Assert

### 10.2 Mock使用
- 对外部依赖使用Mock
- 保持Mock的简单性
- 验证Mock的调用

### 10.3 测试组织
- 按模块组织测试文件
- 使用测试类组织相关测试
- 合理使用pytest的fixture和mark
