"""
数据预处理模块测试
"""

import pytest
from datetime import datetime, timedelta

from database.models import MessageData
from preprocessing.message_filter import MessageFilter
from preprocessing.data_processor import DataProcessor


class TestMessageFilter:
    """消息过滤器测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.filter = MessageFilter()
    
    def test_filter_by_type(self):
        """测试按类型过滤"""
        messages = [
            MessageData(1, 1, 1, 1, 0, 0, 1000, 1, "group1", "文本消息"),  # 文本消息
            MessageData(2, 1, 2, 3, 0, 0, 1001, 2, "group1", "图片消息"),  # 图片消息
            MessageData(3, 1, 3, 1, 0, 0, 1002, 3, "group1", "另一条文本"),  # 文本消息
        ]
        
        filtered = self.filter.filter_messages(messages)
        
        # 应该只保留文本消息（Type=1）
        assert len(filtered) == 2
        assert all(msg.type == 1 for msg in filtered)
    
    def test_filter_by_length(self):
        """测试按长度过滤"""
        messages = [
            MessageData(1, 1, 1, 1, 0, 0, 1000, 1, "group1", "短"),  # 太短
            MessageData(2, 1, 2, 1, 0, 0, 1001, 2, "group1", "正常长度的消息"),  # 正常
            MessageData(3, 1, 3, 1, 0, 0, 1002, 3, "group1", "很长" * 500),  # 太长
        ]
        
        filtered = self.filter.filter_messages(messages)
        
        # 应该只保留正常长度的消息
        assert len(filtered) == 1
        assert filtered[0].str_content == "正常长度的消息"
    
    def test_filter_system_messages(self):
        """测试过滤系统消息"""
        messages = [
            MessageData(1, 1, 1, 1, 0, 0, 1000, 1, "group1", '"张三"撤回了一条消息'),
            MessageData(2, 1, 2, 1, 0, 0, 1001, 2, "group1", "正常的聊天消息"),
            MessageData(3, 1, 3, 1, 0, 0, 1002, 3, "group1", "系统消息：群主已修改群名"),
        ]
        
        filtered = self.filter.filter_messages(messages)
        
        # 应该只保留正常聊天消息
        assert len(filtered) == 1
        assert filtered[0].str_content == "正常的聊天消息"
    
    def test_filter_meaningless_content(self):
        """测试过滤无意义内容"""
        messages = [
            MessageData(1, 1, 1, 1, 0, 0, 1000, 1, "group1", "哈哈哈哈哈"),
            MessageData(2, 1, 2, 1, 0, 0, 1001, 2, "group1", "有意义的讨论内容"),
            MessageData(3, 1, 3, 1, 0, 0, 1002, 3, "group1", "。。。。。"),
        ]
        
        filtered = self.filter.filter_messages(messages)
        
        # 应该只保留有意义的内容
        assert len(filtered) == 1
        assert filtered[0].str_content == "有意义的讨论内容"
    
    def test_remove_duplicates(self):
        """测试去重"""
        messages = [
            MessageData(1, 1, 1, 1, 0, 0, 1000, 1, "group1", "重复消息"),
            MessageData(2, 1, 2, 1, 0, 0, 1001, 2, "group1", "重复消息"),
            MessageData(3, 1, 3, 1, 0, 0, 1002, 3, "group1", "不同消息"),
        ]
        
        filtered = self.filter.filter_messages(messages)
        
        # 应该去除重复消息
        assert len(filtered) == 2
        contents = [msg.str_content for msg in filtered]
        assert "重复消息" in contents
        assert "不同消息" in contents
        assert contents.count("重复消息") == 1
    
    def test_clean_message_content(self):
        """测试消息内容清洗"""
        test_cases = [
            ("  多余空格  ", "多余空格"),
            ("多个\n\n\n换行", "多个 换行"),
            ("&lt;标签&gt;", "<标签>"),
            ("正常内容", "正常内容"),
        ]
        
        for input_content, expected in test_cases:
            cleaned = self.filter.clean_message_content(input_content)
            assert cleaned == expected
    
    def test_extract_keywords(self):
        """测试关键词提取"""
        content = "讨论000001股票，@张三 看看这个链接 https://example.com #热门话题#"
        keywords = self.filter.extract_keywords(content)
        
        assert "000001" in keywords  # 股票代码
        assert "@张三" in keywords  # 用户提及
        assert "https://example.com" in keywords  # URL
        assert "#热门话题#" in keywords  # 话题标签
    
    def test_detect_message_intent(self):
        """测试消息意图检测"""
        test_cases = [
            ("这个怎么样？", "question"),
            ("分享一个链接给大家", "share"),
            ("我觉得这个不错", "discussion"),
            ("看到一个新闻", "news"),
            ("今天天气不错", "chat"),
        ]
        
        for content, expected_intent in test_cases:
            intent = self.filter.detect_message_intent(content)
            assert intent == expected_intent


class TestDataProcessor:
    """数据处理器测试"""
    
    def setup_method(self):
        """测试前准备"""
        # 模拟配置和数据库管理器
        self.processor = DataProcessor()
    
    def test_prepare_analysis_data(self):
        """测试准备分析数据"""
        # 创建测试消息
        now = datetime.now()
        messages = [
            MessageData(1, 1, 1, 1, 0, 0, int(now.timestamp()), 1, "group1", "第一条消息"),
            MessageData(2, 2, 2, 1, 0, 0, int((now + timedelta(minutes=1)).timestamp()), 2, "group1", "第二条消息"),
        ]
        
        # 准备分析数据
        analysis_data = self.processor.prepare_analysis_data("test_group", messages)
        
        assert analysis_data['group_id'] == "test_group"
        assert analysis_data['message_count'] == 2
        assert len(analysis_data['messages']) == 2
        assert 'time_range' in analysis_data
        assert 'participants' in analysis_data
        assert 'keywords' in analysis_data
    
    def test_format_messages_for_ai(self):
        """测试格式化消息给AI"""
        analysis_data = {
            'group_id': 'test_group',
            'message_count': 2,
            'messages': [
                {'time': '10:00:00', 'content': '消息1', 'sender_id': 1},
                {'time': '10:01:00', 'content': '消息2', 'sender_id': 2},
            ],
            'time_range': {
                'start': datetime.now(),
                'end': datetime.now() + timedelta(minutes=1),
                'duration_minutes': 1.0
            },
            'participants': [1, 2],
            'keywords': [('关键词', 2)],
            'message_intents': {'chat': 2}
        }
        
        formatted_text = self.processor.format_messages_for_ai(analysis_data)
        
        assert isinstance(formatted_text, str)
        assert 'test_group' in formatted_text
        assert '消息1' in formatted_text
        assert '消息2' in formatted_text
        assert '群聊分析上下文' in formatted_text
    
    def test_get_analysis_summary(self):
        """测试获取分析摘要"""
        messages_by_group = {
            'group1': [
                MessageData(1, 1, 1, 1, 0, 0, 1000, 1, "group1", "消息1"),
                MessageData(2, 2, 2, 1, 0, 0, 1001, 2, "group1", "消息2"),
            ],
            'group2': [
                MessageData(3, 3, 3, 1, 0, 0, 1002, 3, "group2", "消息3"),
            ]
        }
        
        summary = self.processor.get_analysis_summary(messages_by_group)
        
        assert summary['total_messages'] == 3
        assert summary['total_groups'] == 2
        assert len(summary['most_active_groups']) > 0
        assert summary['most_active_groups'][0]['group_id'] == 'group1'  # group1更活跃
