# 微信群聊内容智能分析系统依赖包

# 核心依赖
openai>=1.0.0                    # 豆包API调用
requests>=2.28.0                 # HTTP请求
# sqlite3                        # SQLite数据库（Python内置，无需安装）

# 数据处理
pandas>=1.5.0                    # 数据处理和分析
numpy>=1.24.0                    # 数值计算

# 网络爬虫和内容解析
beautifulsoup4>=4.11.0           # HTML解析
lxml>=4.9.0                      # XML/HTML解析器
selenium>=4.8.0                  # 网页自动化（如需要）

# 金融数据
tushare>=1.2.89                  # 股票数据接口
akshare>=1.9.0                   # 金融数据获取

# 时间处理
python-dateutil>=2.8.0           # 时间处理工具

# 配置管理
pyyaml>=6.0                      # YAML配置文件解析
python-dotenv>=1.0.0             # 环境变量管理

# 日志和监控
loguru>=0.7.0                    # 高级日志库

# 测试框架
pytest>=7.2.0                   # 单元测试框架
pytest-cov>=4.0.0               # 测试覆盖率
pytest-mock>=3.10.0             # Mock测试

# 开发工具
black>=23.0.0                   # 代码格式化
flake8>=6.0.0                   # 代码检查
mypy>=1.0.0                     # 类型检查

# 其他工具
schedule>=1.2.0                 # 任务调度
rich>=13.0.0                    # 终端美化输出
pywxdump>=1.0.0                 # 微信数据同步
jieba>=0.42.1                   # 中文分词
wordcloud>=1.9.0                # 词云生成
Pillow>=9.0.0                   # 图像处理
fastapi>=0.104.0                # Web API框架
uvicorn>=0.24.0                 # ASGI服务器
python-multipart>=0.0.6         # 文件上传支持
jinja2>=3.1.0                   # 模板引擎
