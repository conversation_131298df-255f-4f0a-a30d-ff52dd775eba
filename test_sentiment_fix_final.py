#!/usr/bin/env python3
"""
测试情感分析器最终修复效果

验证修复后的情感分析器能够处理各种类型的输入，不会出现类型错误。
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_preprocess_text_type_safety():
    """测试预处理文本的类型安全性"""
    print("🧪 测试预处理文本类型安全性...")
    print("=" * 60)
    
    try:
        from analysis.doubao_client import DoubaoClient
        
        # 创建客户端实例
        client = DoubaoClient()
        
        # 测试不同类型的输入
        test_cases = [
            ("正常字符串", "这是一个正常的测试文本"),
            ("空字符串", ""),
            ("None值", None),
            ("字典对象", {"content": "字典内容"}),
            ("列表对象", ["列表", "内容"]),
            ("数字对象", 12345),
            ("布尔值", True)
        ]
        
        print("测试各种类型的输入:")
        for case_name, test_input in test_cases:
            try:
                result = client._preprocess_text(test_input)
                print(f"  {case_name}: ✅ 成功 -> '{result}' (类型: {type(result)})")
            except AttributeError as e:
                if "'dict' object has no attribute 'strip'" in str(e):
                    print(f"  {case_name}: ❌ 原始错误仍存在 -> {e}")
                    return False
                else:
                    print(f"  {case_name}: ❌ 其他AttributeError -> {e}")
                    return False
            except Exception as e:
                print(f"  {case_name}: ❌ 其他错误 -> {e}")
                return False
        
        print("\n✅ 所有预处理操作都安全执行")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_sentiment_analyzer_input_types():
    """测试情感分析器输入类型处理"""
    print("\n🧪 测试情感分析器输入类型处理...")
    print("=" * 60)
    
    try:
        from analysis.sentiment_analyzer import SentimentAnalyzer
        
        # 创建分析器
        analyzer = SentimentAnalyzer()
        
        # 测试不同类型的输入
        test_cases = [
            ("字符串输入", "今天股市表现不错，心情很好！"),
            ("消息字典列表", [
                {
                    'str_content': '今天股市大涨，心情很好！',
                    'sender_name': '张三',
                    'create_datetime': datetime.now()
                }
            ]),
            ("简单字典列表", [{"content": "测试内容"}]),
            ("字符串列表", ["消息1", "消息2"]),
            ("空列表", []),
            ("字典对象", {"content": "字典内容"}),
            ("数字", 12345),
            ("None值", None)
        ]
        
        print("测试各种类型的输入:")
        for case_name, test_input in test_cases:
            try:
                result = analyzer.analyze_sentiment(test_input)
                print(f"  {case_name}: ✅ 成功")
                print(f"    情感评分: {result.get('score', 'N/A')}")
                print(f"    情感标签: {result.get('label', 'N/A')}")
            except AttributeError as e:
                if "'dict' object has no attribute 'strip'" in str(e):
                    print(f"  {case_name}: ❌ 原始错误仍存在 -> {e}")
                    return False
                else:
                    print(f"  {case_name}: ❌ 其他AttributeError -> {e}")
                    return False
            except Exception as e:
                print(f"  {case_name}: ❌ 其他错误 -> {e}")
                import traceback
                traceback.print_exc()
                return False
        
        print("\n✅ 所有情感分析操作都安全执行")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_doubao_client_methods():
    """测试豆包客户端各个方法的类型安全性"""
    print("\n🧪 测试豆包客户端方法类型安全性...")
    print("=" * 60)
    
    try:
        from analysis.doubao_client import DoubaoClient
        
        client = DoubaoClient()
        client.clear_cache()  # 清空缓存确保测试环境干净
        
        # 测试不同类型的输入
        test_inputs = [
            "正常的测试文本内容",
            {"dict": "content"},
            ["list", "content"],
            12345,
            None
        ]
        
        methods_to_test = [
            ("analyze_topic_and_sentiment", lambda x: client.analyze_topic_and_sentiment(x)),
            ("extract_entities", lambda x: client.extract_entities(x)),
            ("generate_summary", lambda x: client.generate_summary(x, max_length=50))
        ]
        
        print("测试各个方法:")
        for method_name, method_func in methods_to_test:
            print(f"\n  测试方法: {method_name}")
            for i, test_input in enumerate(test_inputs, 1):
                try:
                    result = method_func(test_input)
                    print(f"    输入{i} ({type(test_input).__name__}): ✅ 成功 (返回类型: {type(result).__name__})")
                except AttributeError as e:
                    if "'dict' object has no attribute 'strip'" in str(e):
                        print(f"    输入{i}: ❌ 原始错误仍存在 -> {e}")
                        return False
                    else:
                        print(f"    输入{i}: ❌ 其他AttributeError -> {e}")
                        return False
                except Exception as e:
                    print(f"    输入{i}: ⚠️ 其他错误 -> {type(e).__name__}: {e}")
                    # 其他错误（如API调用失败）不算测试失败
        
        print("\n✅ 所有豆包客户端方法都类型安全")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_cache_type_consistency():
    """测试缓存类型一致性"""
    print("\n🧪 测试缓存类型一致性...")
    print("=" * 60)
    
    try:
        from analysis.doubao_client import DoubaoClient
        
        client = DoubaoClient()
        client.clear_cache()
        
        # 模拟不同方法的缓存操作
        test_text = "测试缓存类型一致性"
        
        # 手动设置缓存（模拟可能的类型混乱）
        cache_key_1 = client._get_cache_key("analyze_topic_and_sentiment", test_text)
        cache_key_2 = client._get_cache_key("generate_summary", test_text, "100")
        
        # 设置正确类型的缓存
        client._save_to_cache(cache_key_1, {"main_topic": "测试", "sentiment": {"score": 0.5}})
        client._save_to_cache(cache_key_2, "这是一个测试摘要")
        
        print("测试缓存检索:")
        
        # 测试话题分析缓存
        try:
            result1 = client.analyze_topic_and_sentiment(test_text)
            print(f"  话题分析缓存: ✅ 成功 (类型: {type(result1)})")
        except Exception as e:
            print(f"  话题分析缓存: ❌ 失败 -> {e}")
            return False
        
        # 测试摘要生成缓存
        try:
            result2 = client.generate_summary(test_text, max_length=100)
            print(f"  摘要生成缓存: ✅ 成功 (类型: {type(result2)})")
        except Exception as e:
            print(f"  摘要生成缓存: ❌ 失败 -> {e}")
            return False
        
        # 验证类型正确性
        if isinstance(result1, dict) and isinstance(result2, str):
            print("  ✅ 缓存类型一致性正确")
            return True
        else:
            print(f"  ❌ 缓存类型不正确: result1={type(result1)}, result2={type(result2)}")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def simulate_real_usage_scenario():
    """模拟真实使用场景"""
    print("\n🧪 模拟真实使用场景...")
    print("=" * 60)
    
    try:
        from analysis.sentiment_analyzer import SentimentAnalyzer
        
        analyzer = SentimentAnalyzer()
        
        # 模拟真实的消息数据
        real_messages = [
            {
                'str_content': '今天股市大涨，腾讯涨了5%！',
                'sender_name': '投资者A',
                'create_datetime': datetime.now()
            },
            {
                'str_content': '最近经济形势不太好，有点担心',
                'sender_name': '投资者B',
                'create_datetime': datetime.now()
            },
            {
                'str_content': '大家觉得明天会怎么样？',
                'sender_name': '投资者C',
                'create_datetime': datetime.now()
            }
        ]
        
        print("模拟真实使用场景:")
        
        # 场景1: 单条消息分析
        print("  场景1: 单条消息分析")
        try:
            result1 = analyzer.analyze_sentiment("今天股市表现不错，心情很好！")
            print(f"    ✅ 成功 - 评分: {result1.get('score')}")
        except Exception as e:
            print(f"    ❌ 失败 -> {e}")
            return False
        
        # 场景2: 消息列表分析
        print("  场景2: 消息列表分析")
        try:
            result2 = analyzer.analyze_sentiment(real_messages)
            print(f"    ✅ 成功 - 评分: {result2.get('score')}")
        except Exception as e:
            print(f"    ❌ 失败 -> {e}")
            return False
        
        # 场景3: 空内容处理
        print("  场景3: 空内容处理")
        try:
            result3 = analyzer.analyze_sentiment("")
            print(f"    ✅ 成功 - 评分: {result3.get('score')}")
        except Exception as e:
            print(f"    ❌ 失败 -> {e}")
            return False
        
        # 场景4: 异常输入处理
        print("  场景4: 异常输入处理")
        try:
            result4 = analyzer.analyze_sentiment({"unexpected": "input"})
            print(f"    ✅ 成功 - 评分: {result4.get('score')}")
        except Exception as e:
            print(f"    ❌ 失败 -> {e}")
            return False
        
        print("\n✅ 所有真实使用场景都正常工作")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 情感分析器最终修复效果测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    tests = [
        ("预处理文本类型安全性", test_preprocess_text_type_safety),
        ("情感分析器输入类型处理", test_sentiment_analyzer_input_types),
        ("豆包客户端方法类型安全性", test_doubao_client_methods),
        ("缓存类型一致性", test_cache_type_consistency),
        ("真实使用场景模拟", simulate_real_usage_scenario)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            print(f"\n📋 测试: {test_name}")
            if test_func():
                passed += 1
                print(f"   ✅ {test_name} - 通过")
            else:
                print(f"   ❌ {test_name} - 失败")
        except Exception as e:
            print(f"   ❌ {test_name} - 执行失败: {e}")
    
    print("\n" + "=" * 60)
    print(f"🎯 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 情感分析器类型错误完全修复！")
        print("\n✅ 修复总结:")
        print("1. ✅ 预处理文本方法增加了类型检查和转换")
        print("2. ✅ 情感分析器支持多种输入类型（字符串、列表、字典等）")
        print("3. ✅ 豆包客户端所有方法都具备类型安全性")
        print("4. ✅ 缓存机制类型一致性得到保证")
        print("5. ✅ 真实使用场景都能正常工作")
        print("\n💡 关键改进:")
        print("- 输入参数类型检查和自动转换")
        print("- 消息列表的智能解析")
        print("- 空值和异常输入的优雅处理")
        print("- 默认结果的提供机制")
        print("\n🛡️ 错误预防:")
        print("- 所有字符串操作前都进行类型检查")
        print("- 提供了多种输入格式的兼容性")
        print("- 增加了详细的错误日志记录")
    else:
        print("⚠️ 部分测试失败，需要进一步调查")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
