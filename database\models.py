"""
数据模型定义

定义系统中使用的数据模型类，包括消息数据和分析结果。
"""

from dataclasses import dataclass
from typing import Optional, Dict, Any, List
from datetime import datetime
import json


@dataclass
class MessageData:
    """微信消息数据模型"""
    
    local_id: int                    # 本地ID
    talker_id: int                   # 说话者ID
    msg_svr_id: int                  # 服务器消息ID
    type: int                        # 消息类型
    sub_type: int                    # 消息子类型
    is_sender: int                   # 是否发送者
    create_time: int                 # 创建时间（时间戳）
    sequence: int                    # 序列号
    str_talker: str                  # 群组/用户标识
    str_content: str                 # 消息内容
    display_content: Optional[str]   # 显示内容
    
    @property
    def create_datetime(self) -> datetime:
        """获取创建时间的datetime对象"""
        # 微信时间戳可能是毫秒级，需要转换
        if self.create_time > 1e10:  # 毫秒级时间戳
            timestamp = self.create_time / 1000
        else:  # 秒级时间戳
            timestamp = self.create_time
        return datetime.fromtimestamp(timestamp)
    
    @property
    def is_text_message(self) -> bool:
        """判断是否为文本消息"""
        return self.type == 1
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'local_id': self.local_id,
            'talker_id': self.talker_id,
            'msg_svr_id': self.msg_svr_id,
            'type': self.type,
            'sub_type': self.sub_type,
            'is_sender': self.is_sender,
            'create_time': self.create_time,
            'sequence': self.sequence,
            'str_talker': self.str_talker,
            'str_content': self.str_content or '',
            'display_content': self.display_content,
            'create_datetime': self.create_datetime.isoformat() if self.create_datetime else None
        }


@dataclass
class AnalysisResult:
    """分析结果数据模型"""
    
    id: Optional[int] = None         # 主键ID
    analysis_time: Optional[datetime] = None  # 分析时间
    group_id: str = ""               # 群组ID
    time_window_start: Optional[datetime] = None  # 时间窗口开始
    time_window_end: Optional[datetime] = None    # 时间窗口结束
    message_count: int = 0           # 消息数量
    
    # 话题分析结果
    topics: List[Dict[str, Any]] = None  # 话题列表
    main_topic: str = ""             # 主要话题
    topic_category: str = ""         # 话题分类（股票/时事/链接/其他）
    
    # 情感分析结果
    sentiment_score: float = 0.0     # 情感评分
    sentiment_label: str = ""        # 情感标签
    
    # 深度分析结果
    deep_analysis: Dict[str, Any] = None  # 深度分析结果
    
    # 评分和建议
    overall_score: float = 0.0       # 总体评分
    recommendations: List[str] = None  # 建议列表
    
    # 原始数据
    raw_messages: List[Dict[str, Any]] = None  # 原始消息数据
    ai_response: str = ""            # AI分析响应
    
    # 元数据
    created_at: Optional[datetime] = None  # 创建时间
    updated_at: Optional[datetime] = None  # 更新时间
    
    def __post_init__(self):
        """初始化后处理"""
        if self.topics is None:
            self.topics = []
        if self.deep_analysis is None:
            self.deep_analysis = {}
        if self.recommendations is None:
            self.recommendations = []
        if self.raw_messages is None:
            self.raw_messages = []
        if self.analysis_time is None:
            self.analysis_time = datetime.now()
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.updated_at is None:
            self.updated_at = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        result = {
            'id': self.id,
            'analysis_time': self.analysis_time.isoformat() if self.analysis_time else None,
            'group_id': self.group_id,
            'time_window_start': self.time_window_start.isoformat() if self.time_window_start else None,
            'time_window_end': self.time_window_end.isoformat() if self.time_window_end else None,
            'message_count': self.message_count,
            'topics': self.topics,
            'main_topic': self.main_topic,
            'topic_category': self.topic_category,
            'sentiment_score': self.sentiment_score,
            'sentiment_label': self.sentiment_label,
            'deep_analysis': self.deep_analysis,
            'overall_score': self.overall_score,
            'recommendations': self.recommendations,
            'raw_messages': self.raw_messages,
            'ai_response': self.ai_response,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

        # 添加新字段（如果存在）
        if hasattr(self, 'chat_summary'):
            result['chat_summary'] = self.chat_summary
        if hasattr(self, 'user_statistics'):
            result['user_statistics'] = self.user_statistics
        if hasattr(self, 'discussion_heat'):
            result['discussion_heat'] = self.discussion_heat
        if hasattr(self, 'key_information'):
            result['key_information'] = self.key_information

        return result
    
    def to_json(self) -> str:
        """转换为JSON字符串"""
        return json.dumps(self.to_dict(), ensure_ascii=False, indent=2)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AnalysisResult':
        """从字典创建实例"""
        # 处理时间字段
        for time_field in ['analysis_time', 'time_window_start', 'time_window_end', 'created_at', 'updated_at']:
            if data.get(time_field) and isinstance(data[time_field], str):
                data[time_field] = datetime.fromisoformat(data[time_field])
        
        return cls(**data)
    
    def add_topic(self, topic: str, confidence: float = 1.0, keywords: List[str] = None):
        """添加话题"""
        if keywords is None:
            keywords = []
        
        topic_data = {
            'topic': topic,
            'confidence': confidence,
            'keywords': keywords,
            'timestamp': datetime.now().isoformat()
        }
        self.topics.append(topic_data)
    
    def add_recommendation(self, recommendation: str, priority: str = "medium"):
        """添加建议"""
        rec_data = {
            'content': recommendation,
            'priority': priority,
            'timestamp': datetime.now().isoformat()
        }
        self.recommendations.append(rec_data)
    
    def update_deep_analysis(self, category: str, data: Dict[str, Any]):
        """更新深度分析结果"""
        self.deep_analysis[category] = data
        self.updated_at = datetime.now()


@dataclass
class GroupInfo:
    """群组信息模型"""

    group_id: str                    # 群组ID
    group_name: Optional[str] = None # 群组名称
    member_count: int = 0            # 成员数量
    last_message_time: Optional[datetime] = None  # 最后消息时间
    is_active: bool = True           # 是否活跃
    need_analysis: bool = True       # 是否需要分析
    created_at: Optional[datetime] = None  # 创建时间
    
    def __post_init__(self):
        """初始化后处理"""
        if self.created_at is None:
            self.created_at = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'group_id': self.group_id,
            'group_name': self.group_name,
            'member_count': self.member_count,
            'last_message_time': self.last_message_time.isoformat() if self.last_message_time else None,
            'is_active': self.is_active,
            'need_analysis': self.need_analysis,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
