#!/usr/bin/env python3
"""
测试分析数据结构处理修复

验证修复后的代码能够正确处理包含raw_messages的复杂数据结构。
"""

import sys
import os
from datetime import datetime
from dataclasses import dataclass
from typing import List

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

@dataclass
class MockMessageData:
    """模拟MessageData对象"""
    local_id: int
    talker_id: int
    msg_svr_id: int
    type: int
    sub_type: int
    is_sender: int
    create_time: int
    sequence: int
    str_talker: str
    str_content: str
    display_content: str
    
    @property
    def create_datetime(self):
        return datetime.fromtimestamp(self.create_time)

def test_preprocess_text_with_analysis_data():
    """测试预处理文本方法处理分析数据结构"""
    print("🧪 测试预处理文本方法处理分析数据结构...")
    print("=" * 60)
    
    try:
        from analysis.doubao_client import DoubaoClient
        
        client = DoubaoClient()
        
        # 创建模拟的分析数据结构
        mock_messages = [
            MockMessageData(
                local_id=53641,
                talker_id=12,
                msg_svr_id=7096799454153768774,
                type=1,
                sub_type=0,
                is_sender=0,
                create_time=1753275890,
                sequence=1753275890000,
                str_talker='49531167254@chatroom',
                str_content='差不多得了',
                display_content=''
            ),
            MockMessageData(
                local_id=53642,
                talker_id=12,
                msg_svr_id=8473634689647258250,
                type=1,
                sub_type=0,
                is_sender=0,
                create_time=1753275892,
                sequence=1753275892000,
                str_talker='49531167254@chatroom',
                str_content='那又咋了',
                display_content=''
            ),
            MockMessageData(
                local_id=53643,
                talker_id=12,
                msg_svr_id=7266258074217005675,
                type=1,
                sub_type=0,
                is_sender=0,
                create_time=1753275895,
                sequence=1753275895000,
                str_talker='49531167254@chatroom',
                str_content='习惯下',
                display_content=''
            )
        ]
        
        analysis_data = {
            'raw_messages': mock_messages,
            'message_count': len(mock_messages),
            'time_range': {
                'start': datetime.now(),
                'end': datetime.now()
            }
        }
        
        print("测试包含raw_messages的数据结构:")
        print(f"  输入类型: {type(analysis_data)}")
        print(f"  消息数量: {analysis_data['message_count']}")
        print(f"  消息内容: {[msg.str_content for msg in mock_messages]}")
        
        # 测试预处理方法
        try:
            result = client._preprocess_text(analysis_data)
            print(f"  ✅ 预处理成功")
            print(f"  输出类型: {type(result)}")
            print(f"  输出长度: {len(result)}")
            print(f"  输出内容: {result}")
            
            # 验证内容是否正确提取
            expected_contents = ['差不多得了', '那又咋了', '习惯下']
            for content in expected_contents:
                if content in result:
                    print(f"    ✅ 包含预期内容: {content}")
                else:
                    print(f"    ❌ 缺少预期内容: {content}")
                    return False
            
            return True
            
        except Exception as e:
            print(f"  ❌ 预处理失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_extract_text_from_analysis_data():
    """测试从分析数据中提取文本的方法"""
    print("\n🧪 测试从分析数据中提取文本...")
    print("=" * 60)
    
    try:
        from analysis.doubao_client import DoubaoClient
        
        client = DoubaoClient()
        
        # 测试不同格式的消息数据
        test_cases = [
            {
                'name': 'MessageData对象列表',
                'data': {
                    'raw_messages': [
                        MockMessageData(1, 1, 1, 1, 0, 0, 1753275890, 1753275890000, 'test', '消息1', ''),
                        MockMessageData(2, 1, 2, 1, 0, 0, 1753275891, 1753275891000, 'test', '消息2', ''),
                    ]
                }
            },
            {
                'name': '字典格式消息列表',
                'data': {
                    'raw_messages': [
                        {'str_content': '字典消息1', 'talker_id': 1},
                        {'str_content': '字典消息2', 'talker_id': 2},
                        {'content': '字典消息3', 'talker_id': 3},  # 使用content字段
                    ]
                }
            },
            {
                'name': '混合格式消息',
                'data': {
                    'raw_messages': [
                        MockMessageData(1, 1, 1, 1, 0, 0, 1753275890, 1753275890000, 'test', '对象消息', ''),
                        {'str_content': '字典消息'},
                        '字符串消息'
                    ]
                }
            },
            {
                'name': '空消息列表',
                'data': {
                    'raw_messages': []
                }
            }
        ]
        
        print("测试不同格式的消息数据:")
        for case in test_cases:
            try:
                result = client._extract_text_from_analysis_data(case['data'])
                print(f"  {case['name']}: ✅ 成功")
                print(f"    提取结果: '{result}'")
                print(f"    结果长度: {len(result)}")
            except Exception as e:
                print(f"  {case['name']}: ❌ 失败 -> {e}")
                return False
            print()
        
        print("✅ 所有文本提取测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_sentiment_analyzer_with_analysis_data():
    """测试情感分析器处理分析数据结构"""
    print("\n🧪 测试情感分析器处理分析数据结构...")
    print("=" * 60)
    
    try:
        from analysis.sentiment_analyzer import SentimentAnalyzer
        
        analyzer = SentimentAnalyzer()
        
        # 创建包含raw_messages的分析数据
        mock_messages = [
            MockMessageData(1, 1, 1, 1, 0, 0, 1753275890, 1753275890000, 'test', '今天股市大涨，心情很好！', ''),
            MockMessageData(2, 1, 2, 1, 0, 0, 1753275891, 1753275891000, 'test', '确实不错，继续关注', ''),
            MockMessageData(3, 1, 3, 1, 0, 0, 1753275892, 1753275892000, 'test', '大家都很乐观', ''),
        ]
        
        analysis_data = {
            'raw_messages': mock_messages,
            'message_count': len(mock_messages),
            'time_range': {
                'start': datetime.now(),
                'end': datetime.now()
            }
        }
        
        print("测试情感分析器处理复杂数据结构:")
        print(f"  输入类型: {type(analysis_data)}")
        print(f"  消息数量: {len(mock_messages)}")
        
        try:
            result = analyzer.analyze_sentiment(analysis_data)
            print(f"  ✅ 情感分析成功")
            print(f"  情感评分: {result.get('score', 'N/A')}")
            print(f"  情感标签: {result.get('label', 'N/A')}")
            print(f"  分析说明: {result.get('explanation', 'N/A')}")
            
            # 验证结果格式
            required_fields = ['score', 'label', 'original_label', 'explanation', 'confidence', 'analysis_time']
            for field in required_fields:
                if field in result:
                    print(f"    ✅ 包含必需字段: {field}")
                else:
                    print(f"    ❌ 缺少必需字段: {field}")
                    return False
            
            return True
            
        except Exception as e:
            print(f"  ❌ 情感分析失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_task_manager_message_extraction():
    """测试任务管理器的消息提取方法"""
    print("\n🧪 测试任务管理器消息提取...")
    print("=" * 60)
    
    try:
        from web.task_manager import AsyncTaskManager
        
        task_manager = AsyncTaskManager()
        
        # 创建测试消息
        test_messages = [
            MockMessageData(1, 1, 1, 1, 0, 0, 1753275890, 1753275890000, 'test', '测试消息1', ''),
            MockMessageData(2, 1, 2, 1, 0, 0, 1753275891, 1753275891000, 'test', '测试消息2', ''),
            {'str_content': '字典格式消息', 'talker_id': 3},
            {'content': '另一种字典格式', 'talker_id': 4},
        ]
        
        print("测试消息提取方法:")
        print(f"  输入消息数量: {len(test_messages)}")
        print(f"  消息类型: {[type(msg).__name__ for msg in test_messages]}")
        
        try:
            result = task_manager._extract_message_text(test_messages)
            print(f"  ✅ 消息提取成功")
            print(f"  提取结果类型: {type(result)}")
            print(f"  提取结果长度: {len(result)}")
            print(f"  提取结果内容: {result}")
            
            # 验证是否包含所有消息内容
            expected_contents = ['测试消息1', '测试消息2', '字典格式消息', '另一种字典格式']
            for content in expected_contents:
                if content in result:
                    print(f"    ✅ 包含预期内容: {content}")
                else:
                    print(f"    ❌ 缺少预期内容: {content}")
                    return False
            
            return True
            
        except Exception as e:
            print(f"  ❌ 消息提取失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 分析数据结构处理修复测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    tests = [
        ("预处理文本处理分析数据", test_preprocess_text_with_analysis_data),
        ("从分析数据提取文本", test_extract_text_from_analysis_data),
        ("情感分析器处理分析数据", test_sentiment_analyzer_with_analysis_data),
        ("任务管理器消息提取", test_task_manager_message_extraction)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            print(f"\n📋 测试: {test_name}")
            if test_func():
                passed += 1
                print(f"   ✅ {test_name} - 通过")
            else:
                print(f"   ❌ {test_name} - 失败")
        except Exception as e:
            print(f"   ❌ {test_name} - 执行失败: {e}")
    
    print("\n" + "=" * 60)
    print(f"🎯 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 分析数据结构处理修复成功！")
        print("\n✅ 修复总结:")
        print("1. ✅ 预处理方法能智能识别和处理包含raw_messages的数据结构")
        print("2. ✅ 能正确从MessageData对象中提取str_content字段")
        print("3. ✅ 支持多种消息格式（对象、字典、字符串）")
        print("4. ✅ 情感分析器能处理复杂的分析数据结构")
        print("5. ✅ 任务管理器使用正确的文本提取方法")
        print("\n💡 关键改进:")
        print("- 智能数据结构识别")
        print("- 多格式消息内容提取")
        print("- 向后兼容的API设计")
        print("- 详细的调试日志记录")
    else:
        print("⚠️ 部分测试失败，需要进一步调查")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
