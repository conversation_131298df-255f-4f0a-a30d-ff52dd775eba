#!/usr/bin/env python3
"""
测试情感分析器修复效果

验证修复后的情感分析器不会出现类型错误。
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_doubao_client_type_safety():
    """测试豆包客户端的类型安全性"""
    print("🧪 测试豆包客户端类型安全性...")
    print("=" * 60)
    
    try:
        from analysis.doubao_client import DoubaoClient
        
        # 创建客户端
        client = DoubaoClient()
        
        # 清空缓存以确保测试环境干净
        client.clear_cache()
        
        # 测试文本
        test_texts = [
            "今天股市表现不错，腾讯和阿里都在上涨。",
            "最近的新闻报道显示经济形势良好。",
            "大家对未来的投资前景比较乐观。"
        ]
        
        print("测试话题和情感分析...")
        for i, text in enumerate(test_texts, 1):
            try:
                result = client.analyze_topic_and_sentiment(text)
                print(f"  测试{i}: ✅ 成功")
                print(f"    类型: {type(result)}")
                print(f"    主要话题: {result.get('main_topic', 'N/A')}")
            except Exception as e:
                print(f"  测试{i}: ❌ 失败 - {e}")
                return False
        
        print("\n测试实体提取...")
        for i, text in enumerate(test_texts, 1):
            try:
                result = client.extract_entities(text)
                print(f"  测试{i}: ✅ 成功")
                print(f"    类型: {type(result)}")
                print(f"    股票: {result.get('stocks', [])}")
            except Exception as e:
                print(f"  测试{i}: ❌ 失败 - {e}")
                return False
        
        print("\n测试摘要生成...")
        for i, text in enumerate(test_texts, 1):
            try:
                result = client.generate_summary(text, max_length=50)
                print(f"  测试{i}: ✅ 成功")
                print(f"    类型: {type(result)}")
                print(f"    摘要: {result[:30]}...")
            except Exception as e:
                print(f"  测试{i}: ❌ 失败 - {e}")
                return False
        
        # 测试缓存功能
        print("\n测试缓存功能...")
        cache_stats = client.get_cache_stats()
        print(f"  缓存大小: {cache_stats['cache_size']}")
        print(f"  缓存启用: {cache_stats['cache_enabled']}")
        
        # 重复调用测试缓存
        print("\n测试缓存命中...")
        test_text = test_texts[0]
        
        # 第一次调用
        result1 = client.analyze_topic_and_sentiment(test_text)
        
        # 第二次调用（应该使用缓存）
        result2 = client.analyze_topic_and_sentiment(test_text)
        
        if result1 == result2:
            print("  ✅ 缓存工作正常")
        else:
            print("  ⚠️ 缓存可能有问题")
        
        return True
        
    except Exception as e:
        print(f"❌ 豆包客户端测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_sentiment_analyzer_integration():
    """测试情感分析器集成"""
    print("\n🧪 测试情感分析器集成...")
    print("=" * 60)
    
    try:
        from analysis.sentiment_analyzer import SentimentAnalyzer
        
        # 创建分析器
        analyzer = SentimentAnalyzer()
        
        # 测试消息
        test_messages = [
            {
                'str_content': '今天股市大涨，心情很好！',
                'sender_name': '张三',
                'create_datetime': datetime.now()
            },
            {
                'str_content': '最近经济形势不太好，有点担心。',
                'sender_name': '李四',
                'create_datetime': datetime.now()
            },
            {
                'str_content': '大家觉得明天会怎么样？',
                'sender_name': '王五',
                'create_datetime': datetime.now()
            }
        ]
        
        print("测试情感分析...")
        for i, message in enumerate(test_messages, 1):
            try:
                result = analyzer.analyze_sentiment([message])
                print(f"  测试{i}: ✅ 成功")
                print(f"    情感评分: {result.get('sentiment_score', 'N/A')}")
                print(f"    情感标签: {result.get('sentiment_label', 'N/A')}")
            except Exception as e:
                print(f"  测试{i}: ❌ 失败 - {e}")
                import traceback
                traceback.print_exc()
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 情感分析器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_cache_type_consistency():
    """测试缓存类型一致性"""
    print("\n🧪 测试缓存类型一致性...")
    print("=" * 60)
    
    try:
        from analysis.doubao_client import DoubaoClient
        
        client = DoubaoClient()
        client.clear_cache()
        
        test_text = "测试缓存类型一致性的文本内容"
        
        # 测试不同方法的缓存不会互相干扰
        print("调用不同方法...")
        
        # 1. 话题分析（返回dict）
        topic_result = client.analyze_topic_and_sentiment(test_text)
        print(f"  话题分析结果类型: {type(topic_result)}")
        
        # 2. 实体提取（返回dict）
        entity_result = client.extract_entities(test_text)
        print(f"  实体提取结果类型: {type(entity_result)}")
        
        # 3. 摘要生成（返回str）
        summary_result = client.generate_summary(test_text, max_length=100)
        print(f"  摘要生成结果类型: {type(summary_result)}")
        
        # 验证类型正确性
        if (isinstance(topic_result, dict) and 
            isinstance(entity_result, dict) and 
            isinstance(summary_result, str)):
            print("  ✅ 所有方法返回类型正确")
            
            # 再次调用，测试缓存
            print("\n再次调用测试缓存...")
            topic_result2 = client.analyze_topic_and_sentiment(test_text)
            entity_result2 = client.extract_entities(test_text)
            summary_result2 = client.generate_summary(test_text, max_length=100)
            
            if (isinstance(topic_result2, dict) and 
                isinstance(entity_result2, dict) and 
                isinstance(summary_result2, str)):
                print("  ✅ 缓存后类型仍然正确")
                return True
            else:
                print("  ❌ 缓存后类型错误")
                return False
        else:
            print("  ❌ 方法返回类型错误")
            return False
        
    except Exception as e:
        print(f"❌ 缓存类型一致性测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def simulate_original_error():
    """模拟原始错误场景"""
    print("\n🧪 模拟原始错误场景...")
    print("=" * 60)
    
    try:
        # 模拟可能导致原始错误的情况
        print("模拟混合类型的缓存场景...")
        
        from analysis.doubao_client import DoubaoClient
        
        client = DoubaoClient()
        client.clear_cache()
        
        # 手动在缓存中放入错误类型的数据（模拟bug场景）
        test_text = "测试文本"
        
        # 正常调用
        result = client.generate_summary(test_text, max_length=50)
        print(f"  正常调用结果类型: {type(result)}")
        
        # 验证不会出现 'dict' object has no attribute 'strip' 错误
        if isinstance(result, str):
            stripped_result = result.strip()  # 这里不应该出错
            print(f"  ✅ strip()方法调用成功: {len(stripped_result)} 字符")
            return True
        else:
            print(f"  ❌ 返回类型错误: {type(result)}")
            return False
        
    except AttributeError as e:
        if "'dict' object has no attribute 'strip'" in str(e):
            print(f"  ❌ 原始错误仍然存在: {e}")
            return False
        else:
            print(f"  ❌ 其他AttributeError: {e}")
            return False
    except Exception as e:
        print(f"  ❌ 其他错误: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 情感分析器修复效果测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    tests = [
        ("豆包客户端类型安全性", test_doubao_client_type_safety),
        ("情感分析器集成", test_sentiment_analyzer_integration),
        ("缓存类型一致性", test_cache_type_consistency),
        ("原始错误场景模拟", simulate_original_error)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            print(f"\n📋 测试: {test_name}")
            if test_func():
                passed += 1
                print(f"   ✅ {test_name} - 通过")
            else:
                print(f"   ❌ {test_name} - 失败")
        except Exception as e:
            print(f"   ❌ {test_name} - 执行失败: {e}")
    
    print("\n" + "=" * 60)
    print(f"🎯 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 修复成功！情感分析器不再出现类型错误！")
        print("\n✅ 修复总结:")
        print("1. 添加了类型检查，确保缓存返回正确类型")
        print("2. 改进了缓存键生成，避免不同方法间的冲突")
        print("3. 增强了错误处理，防止类型异常")
        print("4. 添加了缓存管理功能")
    else:
        print("⚠️ 部分测试失败，需要进一步调查")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
