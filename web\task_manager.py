"""
异步任务管理器

管理分析任务的异步执行和状态跟踪。
"""

import asyncio
import uuid
from typing import Dict, Any, Optional, List
from datetime import datetime
from enum import Enum
from dataclasses import dataclass
from loguru import logger
import threading
import queue
import time

from main import WechatAnalysisSystem


class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class TaskInfo:
    """任务信息"""
    task_id: str
    status: TaskStatus
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    progress: int = 0  # 0-100
    current_step: str = ""
    logs: List[str] = None
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    
    def __post_init__(self):
        if self.logs is None:
            self.logs = []


class AsyncTaskManager:
    """异步任务管理器"""
    
    def __init__(self):
        self.tasks: Dict[str, TaskInfo] = {}
        self.analysis_system = WechatAnalysisSystem()
        self.log_queues: Dict[str, queue.Queue] = {}
        
    def create_analysis_task(self, hours: int = 1, sync_data: bool = True,
                           generate_wordcloud: bool = True,
                           send_wordcloud_notification: bool = False,
                           send_analysis_report: bool = False) -> str:
        """
        创建分析任务

        Args:
            hours: 分析时间窗口
            sync_data: 是否同步数据
            generate_wordcloud: 是否生成词云
            send_wordcloud_notification: 是否发送词云通知
            send_analysis_report: 是否发送分析报告

        Returns:
            任务ID
        """
        task_id = str(uuid.uuid4())
        
        task_info = TaskInfo(
            task_id=task_id,
            status=TaskStatus.PENDING,
            created_at=datetime.now()
        )
        
        self.tasks[task_id] = task_info
        self.log_queues[task_id] = queue.Queue()
        
        # 在后台线程中执行任务
        thread = threading.Thread(
            target=self._run_analysis_task,
            args=(task_id, hours, sync_data, generate_wordcloud, send_wordcloud_notification, send_analysis_report)
        )
        thread.daemon = True
        thread.start()
        
        return task_id
    
    def _run_analysis_task(self, task_id: str, hours: int, sync_data: bool,
                          generate_wordcloud: bool, send_wordcloud_notification: bool,
                          send_analysis_report: bool = False):
        """
        执行分析任务
        
        Args:
            task_id: 任务ID
            hours: 分析时间窗口
            sync_data: 是否同步数据
            generate_wordcloud: 是否生成词云
            send_wordcloud_notification: 是否发送词云通知
        """
        task_info = self.tasks[task_id]
        log_queue = self.log_queues[task_id]
        
        try:
            # 更新任务状态
            task_info.status = TaskStatus.RUNNING
            task_info.started_at = datetime.now()
            task_info.current_step = "初始化分析系统"
            task_info.progress = 0
            
            self._add_log(log_queue, "开始执行分析任务")
            self._add_log(log_queue, f"分析参数: 时间窗口={hours}小时, 同步数据={sync_data}, 生成词云={generate_wordcloud}")
            
            # 步骤1: 数据同步
            if sync_data:
                task_info.current_step = "同步微信数据"
                task_info.progress = 10
                self._add_log(log_queue, "开始同步微信数据...")
                
                sync_code, sync_error = self.analysis_system.wechat_sync.sync_data()
                if sync_code != 0:
                    self._add_log(log_queue, f"数据同步失败: {sync_error}")
                else:
                    self._add_log(log_queue, "数据同步成功")
            
            # 步骤2: 获取消息
            task_info.current_step = "获取群聊消息"
            task_info.progress = 20
            self._add_log(log_queue, "开始获取群聊消息...")
            
            messages_by_group = self.analysis_system.db_manager.get_recent_messages(hours)
            group_count = len(messages_by_group)
            total_messages = sum(len(msgs) for msgs in messages_by_group.values())
            
            self._add_log(log_queue, f"获取到 {group_count} 个群组的 {total_messages} 条消息")
            
            if not messages_by_group:
                task_info.current_step = "分析完成"
                task_info.progress = 100
                task_info.status = TaskStatus.COMPLETED
                self._add_log(log_queue, "没有找到需要分析的消息")
                return
            
            # 步骤3: 分析消息
            task_info.current_step = "分析群聊内容"
            task_info.progress = 30
            analysis_results = []
            
            for i, (group_id, messages) in enumerate(messages_by_group.items()):
                progress = 30 + (i / group_count) * 40  # 30-70%
                task_info.progress = int(progress)
                
                self._add_log(log_queue, f"正在分析群组 {group_id[:20]}... ({i+1}/{group_count})")
                
                try:
                    # 使用完整的分析流程
                    analysis_data = {
                        'raw_messages': messages,
                        'message_count': len(messages),
                        'time_range': {
                            'start': min(msg.create_datetime for msg in messages) if messages else None,
                            'end': max(msg.create_datetime for msg in messages) if messages else None
                        }
                    }

                    # 话题分析
                    topic_result = self.analysis_system.topic_analyzer.analyze_topics(analysis_data)

                    # 情感分析
                    sentiment_result = self.analysis_system.sentiment_analyzer.analyze_sentiment(analysis_data)

                    # 深度分析
                    deep_analysis = {}
                    if topic_result.get('topic_category') == 'stock':
                        stock_mentions = self.analysis_system.topic_analyzer.extract_stock_mentions(
                            topic_result.get('entities', {}),
                            topic_result.get('key_information', [])
                        )
                        if stock_mentions:
                            deep_analysis['stock_analysis'] = self.analysis_system.stock_analyzer.analyze_stock_discussion(
                                stock_mentions, topic_result.get('key_information', [])
                            )

                    # 生成聊天摘要
                    chat_summary = self.analysis_system._generate_chat_summary(messages, topic_result, sentiment_result)
                    user_statistics = self.analysis_system._generate_user_statistics(messages)

                    # 构建分析结果
                    from database.models import AnalysisResult

                    result = AnalysisResult(
                        group_id=group_id,
                        analysis_time=datetime.now(),
                        time_window_start=analysis_data['time_range']['start'],
                        time_window_end=analysis_data['time_range']['end'],
                        message_count=analysis_data['message_count'],
                        topics=topic_result.get('topics', []),
                        main_topic=topic_result.get('main_topic', '未知话题'),
                        topic_category=topic_result.get('topic_category', 'other'),
                        sentiment_score=sentiment_result.get('score', 0.0),
                        sentiment_label=sentiment_result.get('label', 'neutral'),
                        deep_analysis=deep_analysis,
                        overall_score=topic_result.get('discussion_heat', 5),
                        recommendations=[],
                        raw_messages=[],  # 不保存原始消息以节省空间
                        ai_response=topic_result.get('ai_response', ''),
                        created_at=datetime.now(),
                        updated_at=datetime.now()
                    )

                    # 添加自定义字段
                    result.chat_summary = chat_summary
                    result.user_statistics = user_statistics
                    result.discussion_heat = topic_result.get('discussion_heat', 5)
                    result.key_information = topic_result.get('key_information', [])

                    # 保存到数据库
                    self.analysis_system.db_manager.save_analysis_result(result)

                    analysis_results.append(result)
                    self._add_log(log_queue, f"群组 {group_id[:20]}... 分析完成")
                except Exception as e:
                    self._add_log(log_queue, f"群组 {group_id[:20]}... 分析失败: {str(e)}")
                    logger.error(f"分析群组 {group_id} 失败: {e}")
            
            # 步骤4: 生成词云
            wordcloud_results = {}
            if generate_wordcloud:
                task_info.current_step = "生成词云图片"
                task_info.progress = 70
                self._add_log(log_queue, "开始生成词云...")
                
                for i, (group_id, messages) in enumerate(messages_by_group.items()):
                    progress = 70 + (i / group_count) * 20  # 70-90%
                    task_info.progress = int(progress)
                    
                    try:
                        message_texts = [msg.str_content for msg in messages]
                        wordcloud_result = self.analysis_system.wordcloud_analyzer.generate_wordcloud(
                            message_texts, group_id
                        )
                        
                        if wordcloud_result.get('success'):
                            wordcloud_results[group_id] = wordcloud_result
                            self._add_log(log_queue, f"群组 {group_id[:20]}... 词云生成成功")
                            
                            # 发送词云通知
                            if send_wordcloud_notification:
                                self.analysis_system.wechat_notifier.send_wordcloud_image(group_id, wordcloud_result)
                        else:
                            self._add_log(log_queue, f"群组 {group_id[:20]}... 词云生成失败")
                    except Exception as e:
                        self._add_log(log_queue, f"群组 {group_id[:20]}... 词云生成异常: {str(e)}")
            
            # 步骤5: 发送通知
            if send_analysis_report:
                task_info.current_step = "发送分析报告"
                task_info.progress = 90
                self._add_log(log_queue, "开始发送分析报告...")

                try:
                    summary = {
                        'total_groups': len(messages_by_group),
                        'total_messages': total_messages,
                        'analysis_results': len(analysis_results),
                        'wordcloud_generated': len(wordcloud_results)
                    }

                    # 将AnalysisResult对象转换为字典
                    analysis_results_dict = [result.to_dict() for result in analysis_results]

                    notification_sent = self.analysis_system.wechat_notifier.send_analysis_report(
                        analysis_results_dict, summary
                    )

                    if notification_sent:
                        self._add_log(log_queue, "分析报告发送成功")
                    else:
                        self._add_log(log_queue, "分析报告发送失败")
                except Exception as e:
                    self._add_log(log_queue, f"发送通知失败: {str(e)}")
            else:
                self._add_log(log_queue, "跳过发送分析报告（未启用）")
            
            # 完成任务
            task_info.current_step = "分析完成"
            task_info.progress = 100
            task_info.status = TaskStatus.COMPLETED
            task_info.completed_at = datetime.now()
            task_info.result = {
                'analysis_results': [result.to_dict() if hasattr(result, 'to_dict') else result for result in analysis_results],
                'wordcloud_results': wordcloud_results,
                'summary': summary
            }
            
            self._add_log(log_queue, "分析任务完成！")
            
        except Exception as e:
            task_info.status = TaskStatus.FAILED
            task_info.error = str(e)
            task_info.completed_at = datetime.now()
            self._add_log(log_queue, f"任务执行失败: {str(e)}")
            logger.error(f"分析任务 {task_id} 执行失败: {e}")
    
    def _add_log(self, log_queue: queue.Queue, message: str):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}"
        log_queue.put(log_message)
    
    def get_task_info(self, task_id: str) -> Optional[TaskInfo]:
        """获取任务信息"""
        return self.tasks.get(task_id)
    
    def get_task_logs(self, task_id: str, last_count: int = 50) -> List[str]:
        """获取任务日志"""
        if task_id not in self.log_queues:
            return []
        
        log_queue = self.log_queues[task_id]
        logs = []
        
        # 获取队列中的所有日志
        while not log_queue.empty():
            try:
                log = log_queue.get_nowait()
                logs.append(log)
            except queue.Empty:
                break
        
        # 更新任务信息中的日志
        if task_id in self.tasks:
            self.tasks[task_id].logs.extend(logs)
            # 只保留最近的日志
            if len(self.tasks[task_id].logs) > 200:
                self.tasks[task_id].logs = self.tasks[task_id].logs[-200:]
        
        # 返回最近的日志
        all_logs = self.tasks[task_id].logs if task_id in self.tasks else logs
        return all_logs[-last_count:] if len(all_logs) > last_count else all_logs
    
    def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        if task_id in self.tasks:
            task_info = self.tasks[task_id]
            if task_info.status in [TaskStatus.PENDING, TaskStatus.RUNNING]:
                task_info.status = TaskStatus.CANCELLED
                task_info.completed_at = datetime.now()
                return True
        return False
    
    def cleanup_old_tasks(self, max_age_hours: int = 24):
        """清理旧任务"""
        current_time = datetime.now()
        tasks_to_remove = []
        
        for task_id, task_info in self.tasks.items():
            age = (current_time - task_info.created_at).total_seconds() / 3600
            if age > max_age_hours:
                tasks_to_remove.append(task_id)
        
        for task_id in tasks_to_remove:
            del self.tasks[task_id]
            if task_id in self.log_queues:
                del self.log_queues[task_id]
        
        logger.info(f"清理了 {len(tasks_to_remove)} 个旧任务")


# 全局任务管理器实例
task_manager = AsyncTaskManager()
