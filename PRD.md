# 聊天内容分析

## 任务: 详细的分析该需求文档PRD.md，并完成以下任务：
- 所有交付物除代码外全部使用中文；
- 交付可直接执行的工程，包括但不限于代码、文档、测试用例等；
- 交付物中需要包含详细的设计文档，包括但不限于需求分析、设计说明、接口文档、测试计划等；
- 交付物中需要包含详细的开发文档，包括但不限于代码实现、代码注释、代码规范等；
- 交付物中需要包含详细的测试文档，包括但不限于测试计划、测试用例、测试报告等；
- 对该项目的远期优化和规划进行合理的拓展；

## 核心特性
### 分析群里的消息并总结当天讨论的主要话题，并对话题进行分类；
- 群聊天信息存在于MSG表中，`D:\wechat\merge_all.db`，表结构参考DB章节的聊天信息表
- 如果是股票类的话题，则分析对应股票的行情：通过网络检索该股票的新闻/资讯/行情数据，并对该该票的行情进行打分；
- 如果是时事类的话题，则通过网络检索相关的内容并结合群里的讨论信息进行总结，并检索相关资讯深入分析该事件的影响，主要是金融/股票方面的直接影响以及潜在影响；
- 如果是链接，则打开对应的链接并对该链接的内容进行归纳总结，同时附上原始链接信息和摘要；
- 如果是其他类的话题，则进行归纳总结，必要的时候进行联网搜索，含有道理/哲学/经验等信息时需要对与之相关的内容进行扩展；

### 信息预处理
- 从MSG表中StrTalker是每个群/人的唯一标识，通过此字段区分不同的聊天群或者人；
- 遍历每个不同的StrTalker并从数据库表MSG中取出最近一个小时（通过`CreateTime`字段）的所有`Type=1`的聊天内容`StrContent`字段；
- 调用大模型，对聊天内容进行分析；具体的prompt以及需要大模型返回的数据格式根据你实际需要设计对应的prompt文件，大模型的调用示例见API章节；
- 根据大模型返回的内容做进一步的分析，此时可能会调用外部的接口等，你需要检索多重可用的中文资料源获取信息；

### 分析结果持久化
- 将分析结果以合适的方式落地到数据库

### 处理结果通知
- 参照API章节中的微信通知将最近一小时的分析结果发送到微信

## API
### 微信通知示例

```python
def send_msg(formatted_datetime):
    webhook_url = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=6c36ed57-c489-4b9c-bb03-e64f69e3ca73"
    # 设置发送的消息内容
    # now = datetime.now()
    # formatted_datetime = now.strftime("%Y-%m-%d %H:%M:%S")
    message = {
        "msgtype": "text",
        "text": {
            "content": f"⚠️⚠️⚠️客服消息提醒⚠️⚠️⚠️\n您有新的客服消息，请及时回复~ \n时间：{formatted_datetime}"
        }
    }
    # 发送 POST 请求
    response = requests.post(webhook_url, json=message)
    # 检查响应状态码
    if response.status_code == 200:
        print(f"{formatted_datetime}——消息发送成功")
    else:
        print(f"{formatted_datetime}——消息发送失败")

```

### 调用大模型示例

```python
export ARK_API_KEY="8e8e774e-996d-400a-a3e5-44e457161541"
import os
from openai import OpenAI

# 请确保您已将 API Key 存储在环境变量 ARK_API_KEY 中
# 初始化Openai客户端，从环境变量中读取您的API Key
client = OpenAI(
    # 此为默认路径，您可根据业务所在地域进行配置
    base_url="https://ark.cn-beijing.volces.com/api/v3/bots",
    # 从环境变量中获取您的 API Key
    api_key=os.environ.get("ARK_API_KEY")
)

# Non-streaming:
print("----- standard request -----")
completion = client.chat.completions.create(
    model="bot-20250709092323-lmvbj",  # bot-20250709092323-lmvbj 为您当前的智能体的ID，注意此处与Chat API存在差异。差异对比详见 SDK使用指南
    messages=[
        {"role": "system", "content": "你是豆包，是由字节跳动开发的 AI 人工智能助手"},
        {"role": "user", "content": "常见的十字花科植物有哪些？"},
    ],
)
print(completion.choices[0].message.content)
if hasattr(completion, "references"):
    print(completion.references)


# Multi-round：
print("----- multiple rounds request -----")
completion = client.chat.completions.create(
    model="bot-20250709092323-lmvbj",  # bot-20250709092323-lmvbj 为您当前的智能体的ID，注意此处与Chat API存在差异。差异对比详见 SDK使用指南
    messages=[  # 通过会话传递历史信息，模型会参考上下文消息
        {"role": "system", "content": "你是豆包，是由字节跳动开发的 AI 人工智能助手"},
        {"role": "user", "content": "花椰菜是什么？"},
        {"role": "assistant", "content": "花椰菜又称菜花、花菜，是一种常见的蔬菜。"},
        {"role": "user", "content": "再详细点"},
    ],
)
print(completion.choices[0].message.content)
if hasattr(completion, "references"):
    print(completion.references)

# Streaming:
print("----- streaming request -----")
stream = client.chat.completions.create(
    model="bot-20250709092323-lmvbj",  # bot-20250709092323-lmvbj 为您当前的智能体的ID，注意此处与Chat API存在差异。差异对比详见 SDK使用指南
    messages=[
        {"role": "system", "content": "你是豆包，是由字节跳动开发的 AI 人工智能助手"},
        {"role": "user", "content": "常见的十字花科植物有哪些？"},
    ],
    stream=True,
)
for chunk in stream:
    if hasattr(chunk, "references"):
        print(chunk.references)
    if not chunk.choices:
        continue
    if chunk.choices[0].delta.content:
        print(chunk.choices[0].delta.content, end="")
print()

```


## DB
### 聊天信息表结构
```sql
CREATE TABLE MSG (
    localId         INTEGER PRIMARY KEY AUTOINCREMENT,
    TalkerId        INT     DEFAULT 0,
    MsgSvrID        INT,
    Type            INT,
    SubType         INT,
    IsSender        INT,
    CreateTime      INT,
    Sequence        INT     DEFAULT 0,
    StatusEx        INT     DEFAULT 0,
    FlagEx          INT,
    Status          INT,
    MsgServerSeq    INT,
    MsgSequence     INT,
    StrTalker       TEXT,
    StrContent      TEXT,
    DisplayContent  TEXT,
    Reserved0       INT     DEFAULT 0,
    Reserved1       INT     DEFAULT 0,
    Reserved2       INT     DEFAULT 0,
    Reserved3       INT     DEFAULT 0,
    Reserved4       TEXT,
    Reserved5       TEXT,
    Reserved6       TEXT,
    CompressContent BLOB,
    BytesExtra      BLOB,
    BytesTrans      BLOB
);

```

#### 数据示例
```sql
27	6	6734325073338615832	1	0	0	1752018383	1752018383000	0	0	2	1	817167384	21057745419@chatroom	现在十万满配 还有优惠		0	2							"
"	
28	6	2290603752645965440	1	0	0	1752018386	1752018386000	0	0	2	1	817167385	21057745419@chatroom	落地不到10		0	2							"
"	
```

