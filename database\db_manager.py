"""
数据库管理器

负责数据库连接、表创建和数据操作。
"""

import sqlite3
import json
from typing import List, Optional, Dict, Any, Tuple
from datetime import datetime, timedelta
from pathlib import Path
from contextlib import contextmanager
from loguru import logger

from .models import MessageData, AnalysisResult, GroupInfo
from config import get_config


class DatabaseManager:
    """数据库管理器类"""
    
    def __init__(self):
        """初始化数据库管理器"""
        self.config = get_config()
        self.wechat_db_path = self.config.get('database.wechat_db_path')
        self.analysis_db_path = self.config.get('database.analysis_db_path')
        
        # 确保分析数据库目录存在
        Path(self.analysis_db_path).parent.mkdir(parents=True, exist_ok=True)
        
        # 初始化分析数据库
        self._init_analysis_database()
    
    def _init_analysis_database(self):
        """初始化分析结果数据库"""
        with self._get_analysis_connection() as conn:
            cursor = conn.cursor()
            
            # 创建分析结果表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS analysis_results (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    analysis_time TEXT NOT NULL,
                    group_id TEXT NOT NULL,
                    time_window_start TEXT,
                    time_window_end TEXT,
                    message_count INTEGER DEFAULT 0,
                    topics TEXT,  -- JSON格式存储话题列表
                    main_topic TEXT,
                    topic_category TEXT,
                    sentiment_score REAL DEFAULT 0.0,
                    sentiment_label TEXT,
                    deep_analysis TEXT,  -- JSON格式存储深度分析结果
                    overall_score REAL DEFAULT 0.0,
                    recommendations TEXT,  -- JSON格式存储建议列表
                    raw_messages TEXT,  -- JSON格式存储原始消息
                    ai_response TEXT,
                    created_at TEXT NOT NULL,
                    updated_at TEXT NOT NULL
                )
            ''')

            # 检查并添加新字段（数据库迁移）
            try:
                cursor.execute("SELECT discussion_heat FROM analysis_results LIMIT 1")
            except Exception:
                logger.info("添加discussion_heat字段到analysis_results表")
                cursor.execute("ALTER TABLE analysis_results ADD COLUMN discussion_heat REAL DEFAULT 5.0")

            try:
                cursor.execute("SELECT key_information FROM analysis_results LIMIT 1")
            except Exception:
                logger.info("添加key_information字段到analysis_results表")
                cursor.execute("ALTER TABLE analysis_results ADD COLUMN key_information TEXT")

            # 创建群组信息表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS group_info (
                    group_id TEXT PRIMARY KEY,
                    group_name TEXT,
                    member_count INTEGER DEFAULT 0,
                    last_message_time TEXT,
                    is_active BOOLEAN DEFAULT 1,
                    need_analysis BOOLEAN DEFAULT 1,
                    created_at TEXT NOT NULL
                )
            ''')

            # 检查并添加need_analysis字段（用于数据库迁移）
            try:
                cursor.execute("SELECT need_analysis FROM group_info LIMIT 1")
            except Exception:
                # 字段不存在，添加字段
                logger.info("添加need_analysis字段到group_info表")
                cursor.execute("ALTER TABLE group_info ADD COLUMN need_analysis BOOLEAN DEFAULT 1")
            
            # 创建索引
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_analysis_group_time ON analysis_results(group_id, analysis_time)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_analysis_category ON analysis_results(topic_category)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_group_active ON group_info(is_active)')
            
            conn.commit()
            logger.info("分析数据库初始化完成")
    
    @contextmanager
    def _get_wechat_connection(self):
        """获取微信数据库连接"""
        if not Path(self.wechat_db_path).exists():
            raise FileNotFoundError(f"微信数据库文件不存在: {self.wechat_db_path}")
        
        conn = sqlite3.connect(self.wechat_db_path)
        conn.row_factory = sqlite3.Row  # 使结果可以通过列名访问
        try:
            yield conn
        finally:
            conn.close()
    
    @contextmanager
    def _get_analysis_connection(self):
        """获取分析数据库连接"""
        conn = sqlite3.connect(self.analysis_db_path)
        conn.row_factory = sqlite3.Row
        try:
            yield conn
        finally:
            conn.close()
    
    def get_recent_messages(self, hours: int = 1, message_types: List[int] = None,
                          filter_by_analysis_groups: bool = True) -> Dict[str, List[MessageData]]:
        """
        获取最近指定小时内的消息

        Args:
            hours: 时间窗口（小时）
            message_types: 消息类型过滤列表，默认为[1]（文本消息）
            filter_by_analysis_groups: 是否只获取需要分析的群组消息

        Returns:
            按群组分组的消息字典
        """
        if message_types is None:
            message_types = [1]  # 默认只获取文本消息

        # 计算时间范围
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=hours)

        # 转换为时间戳（微信可能使用毫秒级时间戳）
        start_timestamp = int(start_time.timestamp())
        end_timestamp = int(end_time.timestamp())

        # 同时检查秒级和毫秒级时间戳
        start_timestamp_ms = start_timestamp * 1000
        end_timestamp_ms = end_timestamp * 1000
        
        # 获取需要分析的群组列表
        analysis_groups = set()
        if filter_by_analysis_groups:
            analysis_groups = self._get_analysis_enabled_groups()

        with self._get_wechat_connection() as conn:
            cursor = conn.cursor()

            # 构建查询条件
            type_placeholders = ','.join(['?' for _ in message_types])
            query = f'''
                SELECT localId, TalkerId, MsgSvrID, Type, SubType, IsSender,
                       CreateTime, Sequence, StrTalker, StrContent, DisplayContent
                FROM MSG
                WHERE Type IN ({type_placeholders})
                  AND (
                    (CreateTime >= ? AND CreateTime <= ?) OR
                    (CreateTime >= ? AND CreateTime <= ?)
                  )
                  AND StrContent IS NOT NULL
                  AND StrContent != ''
                ORDER BY StrTalker, CreateTime
            '''

            params = message_types + [start_timestamp, end_timestamp, start_timestamp_ms, end_timestamp_ms]
            cursor.execute(query, params)
            
            # 按群组分组消息
            messages_by_group = {}
            for row in cursor.fetchall():
                message = MessageData(
                    local_id=row['localId'],
                    talker_id=row['TalkerId'],
                    msg_svr_id=row['MsgSvrID'],
                    type=row['Type'],
                    sub_type=row['SubType'],
                    is_sender=row['IsSender'],
                    create_time=row['CreateTime'],
                    sequence=row['Sequence'],
                    str_talker=row['StrTalker'],
                    str_content=row['StrContent'] or '',
                    display_content=row['DisplayContent']
                )
                
                # 验证消息时间是否在范围内
                msg_time = message.create_datetime
                if start_time <= msg_time <= end_time:
                    group_id = message.str_talker

                    # 如果启用群组过滤，检查群组是否需要分析
                    if filter_by_analysis_groups and analysis_groups and group_id not in analysis_groups:
                        continue

                    if group_id not in messages_by_group:
                        messages_by_group[group_id] = []
                    messages_by_group[group_id].append(message)
            
            logger.info(f"获取到 {sum(len(msgs) for msgs in messages_by_group.values())} 条消息，来自 {len(messages_by_group)} 个群组")
            return messages_by_group

    def _get_analysis_enabled_groups(self) -> set:
        """
        获取启用分析的群组列表

        Returns:
            启用分析的群组ID集合
        """
        try:
            with self._get_analysis_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT group_id FROM group_info
                    WHERE need_analysis = 1 AND is_active = 1
                ''')

                groups = {row['group_id'] for row in cursor.fetchall()}
                logger.debug(f"获取到 {len(groups)} 个启用分析的群组")
                return groups
        except Exception as e:
            logger.warning(f"获取分析群组列表失败: {e}")
            return set()  # 返回空集合，不过滤任何群组
    
    def save_analysis_result(self, result: AnalysisResult) -> int:
        """
        保存分析结果
        
        Args:
            result: 分析结果对象
            
        Returns:
            保存的记录ID
        """
        with self._get_analysis_connection() as conn:
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO analysis_results (
                    analysis_time, group_id, time_window_start, time_window_end,
                    message_count, topics, main_topic, topic_category,
                    sentiment_score, sentiment_label, deep_analysis,
                    overall_score, recommendations, raw_messages,
                    ai_response, created_at, updated_at, discussion_heat, key_information
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                result.analysis_time.isoformat() if result.analysis_time else None,
                result.group_id,
                result.time_window_start.isoformat() if result.time_window_start else None,
                result.time_window_end.isoformat() if result.time_window_end else None,
                result.message_count,
                json.dumps(result.topics, ensure_ascii=False),
                result.main_topic,
                result.topic_category,
                result.sentiment_score,
                result.sentiment_label,
                json.dumps(result.deep_analysis, ensure_ascii=False),
                result.overall_score,
                json.dumps(result.recommendations, ensure_ascii=False),
                json.dumps(result.raw_messages, ensure_ascii=False),
                result.ai_response,
                result.created_at.isoformat() if result.created_at else None,
                result.updated_at.isoformat() if result.updated_at else None,
                getattr(result, 'discussion_heat', 5.0),
                json.dumps(getattr(result, 'key_information', []), ensure_ascii=False)
            ))
            
            conn.commit()
            result_id = cursor.lastrowid
            logger.info(f"保存分析结果，ID: {result_id}, 群组: {result.group_id}")
            return result_id

    def get_analysis_results(self, group_id: Optional[str] = None,
                           start_time: Optional[datetime] = None,
                           end_time: Optional[datetime] = None,
                           limit: int = 100) -> List[AnalysisResult]:
        """
        获取分析结果

        Args:
            group_id: 群组ID过滤
            start_time: 开始时间
            end_time: 结束时间
            limit: 结果数量限制

        Returns:
            分析结果列表
        """
        with self._get_analysis_connection() as conn:
            cursor = conn.cursor()

            query = 'SELECT * FROM analysis_results WHERE 1=1'
            params = []

            if group_id:
                query += ' AND group_id = ?'
                params.append(group_id)

            if start_time:
                query += ' AND analysis_time >= ?'
                params.append(start_time.isoformat())

            if end_time:
                query += ' AND analysis_time <= ?'
                params.append(end_time.isoformat())

            query += ' ORDER BY analysis_time DESC LIMIT ?'
            params.append(limit)

            cursor.execute(query, params)

            results = []
            for row in cursor.fetchall():
                result = AnalysisResult(
                    id=row['id'],
                    analysis_time=datetime.fromisoformat(row['analysis_time']) if row['analysis_time'] else None,
                    group_id=row['group_id'],
                    time_window_start=datetime.fromisoformat(row['time_window_start']) if row['time_window_start'] else None,
                    time_window_end=datetime.fromisoformat(row['time_window_end']) if row['time_window_end'] else None,
                    message_count=row['message_count'],
                    topics=json.loads(row['topics']) if row['topics'] else [],
                    main_topic=row['main_topic'] or '',
                    topic_category=row['topic_category'] or '',
                    sentiment_score=row['sentiment_score'] or 0.0,
                    sentiment_label=row['sentiment_label'] or '',
                    deep_analysis=json.loads(row['deep_analysis']) if row['deep_analysis'] else {},
                    overall_score=row['overall_score'] or 0.0,
                    recommendations=json.loads(row['recommendations']) if row['recommendations'] else [],
                    raw_messages=json.loads(row['raw_messages']) if row['raw_messages'] else [],
                    ai_response=row['ai_response'] or '',
                    created_at=datetime.fromisoformat(row['created_at']) if row['created_at'] else None,
                    updated_at=datetime.fromisoformat(row['updated_at']) if row['updated_at'] else None
                )
                results.append(result)

            return results

    def update_group_info(self, group_info: GroupInfo):
        """更新群组信息"""
        with self._get_analysis_connection() as conn:
            cursor = conn.cursor()

            cursor.execute('''
                INSERT OR REPLACE INTO group_info (
                    group_id, group_name, member_count, last_message_time, is_active, need_analysis, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                group_info.group_id,
                group_info.group_name,
                group_info.member_count,
                group_info.last_message_time.isoformat() if group_info.last_message_time else None,
                group_info.is_active,
                getattr(group_info, 'need_analysis', True),  # 默认需要分析
                group_info.created_at.isoformat() if group_info.created_at else None
            ))

            conn.commit()
            logger.debug(f"更新群组信息: {group_info.group_id}")

    def get_active_groups(self) -> List[GroupInfo]:
        """获取活跃群组列表"""
        with self._get_analysis_connection() as conn:
            cursor = conn.cursor()

            cursor.execute('''
                SELECT * FROM group_info
                WHERE is_active = 1
                ORDER BY last_message_time DESC
            ''')

            groups = []
            for row in cursor.fetchall():
                group = GroupInfo(
                    group_id=row['group_id'],
                    group_name=row['group_name'],
                    member_count=row['member_count'] or 0,
                    last_message_time=datetime.fromisoformat(row['last_message_time']) if row['last_message_time'] else None,
                    is_active=bool(row['is_active']),
                    created_at=datetime.fromisoformat(row['created_at']) if row['created_at'] else None,
                    need_analysis=bool(row['need_analysis']) if row['need_analysis'] is not None else True
                )
                groups.append(group)

            return groups

    def get_analysis_statistics(self, days: int = 7) -> Dict[str, Any]:
        """
        获取分析统计信息

        Args:
            days: 统计天数

        Returns:
            统计信息字典
        """
        end_time = datetime.now()
        start_time = end_time - timedelta(days=days)

        with self._get_analysis_connection() as conn:
            cursor = conn.cursor()

            # 总分析次数
            cursor.execute('''
                SELECT COUNT(*) as total_count
                FROM analysis_results
                WHERE analysis_time >= ?
            ''', (start_time.isoformat(),))
            total_count = cursor.fetchone()['total_count']

            # 按话题分类统计
            cursor.execute('''
                SELECT topic_category, COUNT(*) as count
                FROM analysis_results
                WHERE analysis_time >= ? AND topic_category IS NOT NULL
                GROUP BY topic_category
            ''', (start_time.isoformat(),))
            category_stats = {row['topic_category']: row['count'] for row in cursor.fetchall()}

            # 按群组统计
            cursor.execute('''
                SELECT group_id, COUNT(*) as count
                FROM analysis_results
                WHERE analysis_time >= ?
                GROUP BY group_id
                ORDER BY count DESC
                LIMIT 10
            ''', (start_time.isoformat(),))
            group_stats = {row['group_id']: row['count'] for row in cursor.fetchall()}

            # 平均评分
            cursor.execute('''
                SELECT AVG(overall_score) as avg_score
                FROM analysis_results
                WHERE analysis_time >= ? AND overall_score > 0
            ''', (start_time.isoformat(),))
            avg_score = cursor.fetchone()['avg_score'] or 0.0

            return {
                'period_days': days,
                'total_analyses': total_count,
                'category_distribution': category_stats,
                'top_groups': group_stats,
                'average_score': round(avg_score, 2),
                'generated_at': datetime.now().isoformat()
            }

    def get_analysis_results(self, group_id: str = None, hours: int = 24,
                           limit: int = 50) -> List[AnalysisResult]:
        """
        获取分析结果

        Args:
            group_id: 群组ID过滤
            hours: 时间范围（小时）
            limit: 结果数量限制

        Returns:
            分析结果列表
        """
        try:
            with self._get_analysis_connection() as conn:
                # 设置row_factory以便按列名访问
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                # 计算时间范围
                end_time = datetime.now()
                start_time = end_time - timedelta(hours=hours)

                # 构建查询条件
                where_conditions = ["analysis_time >= ?"]
                params = [start_time.isoformat()]

                if group_id:
                    where_conditions.append("group_id = ?")
                    params.append(group_id)

                where_clause = " AND ".join(where_conditions)

                query = f'''
                    SELECT * FROM analysis_results
                    WHERE {where_clause}
                    ORDER BY analysis_time DESC
                    LIMIT ?
                '''
                params.append(limit)

                cursor.execute(query, params)
                rows = cursor.fetchall()

                results = []
                for row in rows:
                    result = AnalysisResult(
                        id=row['id'],
                        analysis_time=datetime.fromisoformat(row['analysis_time']) if row['analysis_time'] else None,
                        group_id=row['group_id'],
                        time_window_start=datetime.fromisoformat(row['time_window_start']) if row['time_window_start'] else None,
                        time_window_end=datetime.fromisoformat(row['time_window_end']) if row['time_window_end'] else None,
                        message_count=row['message_count'],
                        topics=json.loads(row['topics']) if row['topics'] else [],
                        main_topic=row['main_topic'],
                        topic_category=row['topic_category'],
                        sentiment_score=row['sentiment_score'],
                        sentiment_label=row['sentiment_label'],
                        deep_analysis=json.loads(row['deep_analysis']) if row['deep_analysis'] else {},
                        overall_score=row['overall_score'],
                        recommendations=json.loads(row['recommendations']) if row['recommendations'] else [],
                        raw_messages=json.loads(row['raw_messages']) if row['raw_messages'] else [],
                        ai_response=row['ai_response'],
                        created_at=datetime.fromisoformat(row['created_at']) if row['created_at'] else None,
                        updated_at=datetime.fromisoformat(row['updated_at']) if row['updated_at'] else None
                    )

                    # 添加新字段（如果存在）
                    if 'discussion_heat' in row.keys() and row['discussion_heat'] is not None:
                        result.discussion_heat = float(row['discussion_heat'])
                    else:
                        result.discussion_heat = 5.0

                    if 'key_information' in row.keys() and row['key_information']:
                        try:
                            result.key_information = json.loads(row['key_information'])
                        except json.JSONDecodeError:
                            result.key_information = []
                    else:
                        result.key_information = []

                    results.append(result)

                logger.debug(f"获取到 {len(results)} 条分析结果")
                return results

        except Exception as e:
            logger.error(f"获取分析结果失败: {e}")
            return []

    def get_recent_messages_for_group(self, group_id: str, hours: int = 24,
                                    limit: int = 100) -> List[MessageData]:
        """
        获取指定群组的最近消息

        Args:
            group_id: 群组ID
            hours: 时间范围（小时）
            limit: 消息数量限制

        Returns:
            消息列表
        """
        try:
            # 计算时间范围
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=hours)

            # 转换为时间戳（秒和毫秒）
            start_timestamp = int(start_time.timestamp())
            end_timestamp = int(end_time.timestamp())
            start_timestamp_ms = start_timestamp * 1000
            end_timestamp_ms = end_timestamp * 1000

            with self._get_wechat_connection() as conn:
                cursor = conn.cursor()

                # 简化查询，支持秒级和毫秒级时间戳
                query = '''
                    SELECT localId, TalkerId, MsgSvrID, Type, SubType, IsSender,
                           CreateTime, Sequence, StrTalker, StrContent, DisplayContent
                    FROM MSG
                    WHERE StrTalker = ?
                      AND Type = 1
                      AND StrContent IS NOT NULL
                      AND StrContent != ''
                    ORDER BY CreateTime DESC
                    LIMIT ?
                '''

                params = [group_id, limit * 2]  # 获取更多数据，然后在Python中过滤
                cursor.execute(query, params)

                messages = []
                filtered_count = 0
                for row in cursor.fetchall():
                    try:
                        # 处理时间戳
                        create_time = row['CreateTime']
                        if create_time > 1000000000000:  # 毫秒级时间戳
                            create_datetime = datetime.fromtimestamp(create_time / 1000)
                        else:  # 秒级时间戳
                            create_datetime = datetime.fromtimestamp(create_time)

                        # 验证时间范围
                        if start_time <= create_datetime <= end_time:
                            message = MessageData(
                                local_id=row['localId'],
                                talker_id=row['TalkerId'],
                                msg_svr_id=row['MsgSvrID'],
                                type=row['Type'],
                                sub_type=row['SubType'],
                                is_sender=bool(row['IsSender']),
                                create_time=row['CreateTime'],
                                sequence=row['Sequence'],
                                str_talker=row['StrTalker'],
                                str_content=row['StrContent'],
                                display_content=row['DisplayContent']
                            )
                            messages.append(message)
                            filtered_count += 1

                            # 达到限制数量就停止
                            if filtered_count >= limit:
                                break

                    except Exception as e:
                        logger.warning(f"处理消息失败: {e}")
                        continue

                logger.debug(f"获取到群组 {group_id} 的 {len(messages)} 条消息")
                return messages

        except Exception as e:
            logger.error(f"获取群组消息失败: {e}")
            return []

    def set_group_analysis_status(self, group_id: str, need_analysis: bool) -> bool:
        """
        设置群组分析状态

        Args:
            group_id: 群组ID
            need_analysis: 是否需要分析

        Returns:
            操作是否成功
        """
        try:
            with self._get_analysis_connection() as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    UPDATE group_info
                    SET need_analysis = ?
                    WHERE group_id = ?
                ''', (need_analysis, group_id))

                if cursor.rowcount == 0:
                    # 如果群组不存在，创建新记录
                    cursor.execute('''
                        INSERT INTO group_info (group_id, need_analysis, created_at)
                        VALUES (?, ?, ?)
                    ''', (group_id, need_analysis, datetime.now().isoformat()))

                conn.commit()
                logger.info(f"设置群组 {group_id} 分析状态为: {need_analysis}")
                return True

        except Exception as e:
            logger.error(f"设置群组分析状态失败: {e}")
            return False

    def get_group_analysis_status(self, group_id: str) -> Optional[bool]:
        """
        获取群组分析状态

        Args:
            group_id: 群组ID

        Returns:
            分析状态，None表示群组不存在
        """
        try:
            with self._get_analysis_connection() as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    SELECT need_analysis FROM group_info WHERE group_id = ?
                ''', (group_id,))

                result = cursor.fetchone()
                return bool(result['need_analysis']) if result else None

        except Exception as e:
            logger.error(f"获取群组分析状态失败: {e}")
            return None
