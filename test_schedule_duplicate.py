#!/usr/bin/env python3
"""
测试定时任务是否会导致重复发送分析报告
"""

import sys
import os
import time
from datetime import datetime
from unittest.mock import patch, MagicMock

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_schedule_manager_execution():
    """测试定时任务管理器的执行逻辑"""
    print("🧪 测试定时任务管理器执行...")
    
    # 记录发送次数
    send_count = 0
    create_task_count = 0
    original_send_method = None
    original_create_task_method = None
    
    def mock_send_analysis_report(*args, **kwargs):
        nonlocal send_count
        send_count += 1
        print(f"   📧 第{send_count}次调用send_analysis_report")
        return True
    
    def mock_create_analysis_task(*args, **kwargs):
        nonlocal create_task_count
        create_task_count += 1
        print(f"   🔧 第{create_task_count}次调用create_analysis_task")
        print(f"      参数: args={args}, kwargs={kwargs}")
        # 返回一个模拟的任务ID
        return f"mock_task_{create_task_count}"
    
    try:
        from web.schedule_manager import ScheduleManager
        from web.task_manager import AsyncTaskManager
        from notification.wechat_notifier import WechatNotifier
        
        # 保存原始方法
        original_send_method = WechatNotifier.send_analysis_report
        original_create_task_method = AsyncTaskManager.create_analysis_task
        
        # 替换为模拟方法
        WechatNotifier.send_analysis_report = mock_send_analysis_report
        AsyncTaskManager.create_analysis_task = mock_create_analysis_task
        
        print("   创建定时任务管理器...")
        schedule_manager = ScheduleManager()
        
        # 模拟配置
        config = {
            'interval_minutes': 120,
            'send_analysis_report': True,
            'send_wordcloud': False,
            'enabled': True
        }
        
        print("   执行定时分析任务...")
        schedule_manager._execute_scheduled_analysis(config)
        
        print(f"\n📊 测试结果:")
        print(f"   create_analysis_task调用次数: {create_task_count}")
        print(f"   send_analysis_report调用次数: {send_count}")
        
        if create_task_count == 1:
            print("   ✅ 定时任务只创建了一个分析任务")
        else:
            print(f"   ❌ 定时任务创建了{create_task_count}个分析任务，可能有重复")
        
        if send_count == 0:
            print("   ✅ 没有直接调用send_analysis_report（正常，应该由任务管理器处理）")
        else:
            print(f"   ⚠️ 直接调用了{send_count}次send_analysis_report")
        
        return create_task_count == 1 and send_count == 0
        
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 恢复原始方法
        if original_send_method:
            WechatNotifier.send_analysis_report = original_send_method
        if original_create_task_method:
            AsyncTaskManager.create_analysis_task = original_create_task_method

def test_task_manager_send_logic():
    """测试任务管理器的发送逻辑"""
    print("\n🧪 测试任务管理器发送逻辑...")
    
    # 记录发送次数
    send_count = 0
    original_send_method = None
    
    def mock_send_analysis_report(*args, **kwargs):
        nonlocal send_count
        send_count += 1
        print(f"   📧 第{send_count}次调用send_analysis_report")
        print(f"      参数数量: {len(args)}")
        if len(args) > 0:
            print(f"      分析结果数量: {len(args[0])}")
        return True
    
    try:
        from web.task_manager import AsyncTaskManager
        from notification.wechat_notifier import WechatNotifier
        
        # 保存原始方法
        original_send_method = WechatNotifier.send_analysis_report
        
        # 替换为模拟方法
        WechatNotifier.send_analysis_report = mock_send_analysis_report
        
        print("   创建任务管理器...")
        task_manager = AsyncTaskManager()
        
        # 测试1: 启用发送分析报告
        print("   测试1: 启用发送分析报告...")
        task_id1 = task_manager.create_analysis_task(
            hours=1,
            sync_data=False,
            generate_wordcloud=False,
            send_wordcloud_notification=False,
            send_analysis_report=True  # 启用
        )
        
        # 等待任务完成
        for i in range(30):
            task_info = task_manager.get_task_info(task_id1)
            if task_info and task_info.status.name in ['COMPLETED', 'FAILED']:
                break
            time.sleep(1)
        
        send_count_test1 = send_count
        
        # 测试2: 禁用发送分析报告
        print("   测试2: 禁用发送分析报告...")
        send_count = 0  # 重置计数
        
        task_id2 = task_manager.create_analysis_task(
            hours=1,
            sync_data=False,
            generate_wordcloud=False,
            send_wordcloud_notification=False,
            send_analysis_report=False  # 禁用
        )
        
        # 等待任务完成
        for i in range(30):
            task_info = task_manager.get_task_info(task_id2)
            if task_info and task_info.status.name in ['COMPLETED', 'FAILED']:
                break
            time.sleep(1)
        
        send_count_test2 = send_count
        
        print(f"\n📊 测试结果:")
        print(f"   启用发送时调用次数: {send_count_test1}")
        print(f"   禁用发送时调用次数: {send_count_test2}")
        
        if send_count_test1 <= 1 and send_count_test2 == 0:
            print("   ✅ 任务管理器发送逻辑正常")
            return True
        else:
            print("   ❌ 任务管理器发送逻辑有问题")
            return False
        
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 恢复原始方法
        if original_send_method:
            WechatNotifier.send_analysis_report = original_send_method

def test_potential_double_call():
    """测试可能的双重调用场景"""
    print("\n🧪 测试可能的双重调用场景...")
    
    # 记录调用详情
    call_details = []
    original_send_method = None
    
    def mock_send_analysis_report(*args, **kwargs):
        import traceback
        call_info = {
            'timestamp': datetime.now().strftime("%H:%M:%S.%f"),
            'args_count': len(args),
            'stack': traceback.format_stack()[-3:-1]  # 获取调用栈
        }
        call_details.append(call_info)
        print(f"   📧 第{len(call_details)}次调用send_analysis_report at {call_info['timestamp']}")
        print(f"      调用来源: {call_info['stack'][-1].strip()}")
        return True
    
    try:
        from notification.wechat_notifier import WechatNotifier
        
        # 保存原始方法
        original_send_method = WechatNotifier.send_analysis_report
        
        # 替换为模拟方法
        WechatNotifier.send_analysis_report = mock_send_analysis_report
        
        # 模拟可能导致重复调用的场景
        print("   模拟分析结果...")
        analysis_results = [
            {
                'group_id': 'test_group_1',
                'main_topic': '测试话题1',
                'sentiment_score': 0.5,
                'discussion_heat': 7.0,
                'topics': ['话题1', '话题2']
            }
        ]
        summary = {
            'total_groups': 1,
            'total_messages': 10,
            'time_window_hours': 1
        }
        
        print("   直接调用微信通知器...")
        notifier = WechatNotifier()
        result = notifier.send_analysis_report(analysis_results, summary)
        
        print(f"\n📊 测试结果:")
        print(f"   总调用次数: {len(call_details)}")
        
        if len(call_details) == 1:
            print("   ✅ 只调用了一次，没有重复")
            return True
        else:
            print("   ❌ 发现重复调用")
            for i, call in enumerate(call_details, 1):
                print(f"     调用{i}: {call['timestamp']}")
            return False
        
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 恢复原始方法
        if original_send_method:
            WechatNotifier.send_analysis_report = original_send_method

def main():
    """主测试函数"""
    print("🚀 开始测试定时任务重复发送问题...")
    print("=" * 60)
    
    tests = [
        ("定时任务管理器执行测试", test_schedule_manager_execution),
        ("任务管理器发送逻辑测试", test_task_manager_send_logic),
        ("双重调用场景测试", test_potential_double_call)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            print(f"\n📋 测试: {test_name}")
            if test_func():
                passed += 1
                print(f"   ✅ {test_name} - 通过")
            else:
                print(f"   ❌ {test_name} - 失败")
        except Exception as e:
            print(f"   ❌ {test_name} - 执行失败: {e}")
    
    print("\n" + "=" * 60)
    print(f"🎯 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 没有发现重复发送问题！")
        print("\n💡 可能的原因:")
        print("   1. 问题已经通过之前的修复解决")
        print("   2. 重复发送可能发生在特定条件下")
        print("   3. 可能是用户观察到的是不同时间的两次正常发送")
    else:
        print("⚠️ 发现了重复发送问题，需要进一步调查")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
