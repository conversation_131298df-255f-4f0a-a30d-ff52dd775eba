"""
企业微信通知器

负责通过企业微信机器人发送分析结果通知。
"""

import requests
import json
import time
from typing import Dict, Any, List, Optional
from datetime import datetime
from loguru import logger

from config import get_config
from database.db_manager import DatabaseManager


class WechatNotifier:
    """企业微信通知器类"""
    
    def __init__(self):
        """初始化企业微信通知器"""
        self.config = get_config()
        self.notification_config = self.config.get_wechat_notification_config()
        self.db_manager = DatabaseManager()

        self.webhook_url = self.notification_config.get('webhook_url')
        self.message_template = self.notification_config.get('message_template',
                                                           '📊 微信群聊分析报告\n时间：{datetime}\n\n{content}')

        # 限流控制
        self.last_send_time = 0
        self.rate_limit_interval = 1.0  # 1秒间隔

        # 添加去重机制
        self.recent_messages = {}  # 存储最近发送的消息哈希
        self.dedup_window = 300  # 去重时间窗口（5分钟）

        # 如果没有配置webhook_url，记录警告但不抛出异常
        if not self.webhook_url:
            logger.warning("企业微信Webhook URL未配置，通知功能将被禁用")
            self.enabled = False
        else:
            self.enabled = True

    def _get_group_display_name(self, group_id: str) -> str:
        """
        获取群组显示名称

        Args:
            group_id: 群组ID

        Returns:
            群组显示名称，如果没有设置群组名称则返回截断的ID
        """
        try:
            groups = self.db_manager.get_active_groups()
            for group in groups:
                if group.group_id == group_id:
                    if group.group_name:
                        return group.group_name
                    break

            # 如果没有找到群组名称，返回截断的ID
            return f"{group_id[:20]}..." if len(group_id) > 20 else group_id

        except Exception as e:
            logger.warning(f"获取群组名称失败 {group_id}: {e}")
            return f"{group_id[:20]}..." if len(group_id) > 20 else group_id
    
    def send_analysis_report(self, analysis_results: List[Dict[str, Any]],
                           summary: Dict[str, Any]) -> bool:
        """
        发送分析报告

        Args:
            analysis_results: 分析结果列表
            summary: 分析摘要

        Returns:
            发送是否成功
        """
        if not self.enabled:
            logger.debug("企业微信通知未启用，跳过发送分析报告")
            return True

        try:
            # 格式化报告内容
            report_content = self._format_analysis_report(analysis_results, summary)

            # 构建消息
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            message_content = self.message_template.format(
                datetime=current_time,
                content=report_content
            )

            # 检查是否为重复消息
            if self._is_duplicate_message(message_content):
                logger.info("检测到重复的分析报告，跳过发送")
                return True

            # 发送消息
            result = self._send_message(message_content)

            # 如果发送成功，记录消息哈希用于去重
            if result:
                self._record_message(message_content)

            return result

        except Exception as e:
            logger.error(f"发送分析报告失败: {e}")
            return False
    
    def send_error_notification(self, error_message: str, context: str = "") -> bool:
        """
        发送错误通知
        
        Args:
            error_message: 错误信息
            context: 错误上下文
            
        Returns:
            发送是否成功
        """
        try:
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            content = f"⚠️ 系统错误通知\n时间：{current_time}\n错误：{error_message}"
            if context:
                content += f"\n上下文：{context}"
            
            return self._send_message(content)
            
        except Exception as e:
            logger.error(f"发送错误通知失败: {e}")
            return False
    
    def send_system_status(self, status: Dict[str, Any]) -> bool:
        """
        发送系统状态通知
        
        Args:
            status: 系统状态信息
            
        Returns:
            发送是否成功
        """
        try:
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            content = f"🔧 系统状态报告\n时间：{current_time}\n"
            content += f"运行状态：{status.get('status', '未知')}\n"
            content += f"处理消息数：{status.get('processed_messages', 0)}\n"
            content += f"分析群组数：{status.get('analyzed_groups', 0)}\n"
            
            if status.get('errors'):
                content += f"错误数量：{len(status['errors'])}\n"
            
            return self._send_message(content)
            
        except Exception as e:
            logger.error(f"发送系统状态失败: {e}")
            return False

    def send_wordcloud_image(self, group_id: str, wordcloud_result: Dict[str, Any]) -> bool:
        """
        发送词云图片

        Args:
            group_id: 群组ID
            wordcloud_result: 词云生成结果

        Returns:
            发送是否成功
        """
        if not self.enabled:
            logger.debug("企业微信通知未启用，跳过发送词云图片")
            return True

        try:
            if not wordcloud_result.get('success'):
                error_msg = wordcloud_result.get('error', '词云生成失败')
                return self.send_error_notification(f"词云生成失败: {error_msg}", f"群组: {group_id}")

            # 获取群组显示名称
            group_name = self._get_group_display_name(group_id)

            # 构建图片消息
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            # 检查是否有base64和md5字段
            if 'base64' in wordcloud_result and 'md5' in wordcloud_result:
                # 发送图片消息（企业微信支持base64图片）
                image_message = {
                    "msgtype": "image",
                    "image": {
                        "base64": wordcloud_result['base64'],
                        "md5": wordcloud_result['md5']
                    }
                }

                # 先发送图片
                image_sent = self._send_message_raw(image_message)
            else:
                # 如果没有base64数据，只发送文字说明
                logger.warning(f"词云结果缺少base64数据，只发送文字说明")
                image_sent = True

            # 再发送说明文字
            caption = f"📊 {group_name} 词云分析\n"
            caption += f"时间：{current_time}\n"
            caption += f"关键词数量：{wordcloud_result.get('word_count', 0)}\n"

            # 添加热门词汇
            top_words = wordcloud_result.get('top_words', [])[:10]
            if top_words:
                caption += "热门词汇：" + "、".join([f"{word}({freq})" for word, freq in top_words])

            text_sent = self._send_message(caption)

            return image_sent and text_sent

        except Exception as e:
            logger.error(f"发送词云图片失败: {e}")
            return False
    
    def _format_analysis_report(self, analysis_results: List[Dict[str, Any]], 
                              summary: Dict[str, Any]) -> str:
        """格式化分析报告内容"""
        if not analysis_results:
            return "📝 本次分析未发现活跃讨论"
        
        report_lines = []
        
        # 添加摘要信息
        report_lines.append("📊 分析摘要")
        report_lines.append(f"• 分析群组：{summary.get('total_groups', 0)} 个")
        report_lines.append(f"• 处理消息：{summary.get('total_messages', 0)} 条")
        report_lines.append(f"• 时间窗口：{summary.get('time_window_hours', 1)} 小时")
        report_lines.append("")
        
        # 按优先级排序分析结果
        sorted_results = sorted(analysis_results,
                              key=lambda x: self._get_priority_score(x),
                              reverse=True)

        # 显示所有分析结果，不限制数量
        for i, result in enumerate(sorted_results, 1):
            report_lines.append(f"🔍 群组分析 {i}")

            # 使用群组名称替代ID
            group_id = result.get('group_id', '未知')
            group_name = self._get_group_display_name(group_id)
            report_lines.append(f"群组：{group_name}")

            # 主要话题
            main_topic = result.get('main_topic', '未知话题')
            topic_category = result.get('topic_category', 'other')
            category_emoji = self._get_category_emoji(topic_category)
            report_lines.append(f"话题：{category_emoji} {main_topic}")
            
            # 情感分析
            sentiment = result.get('sentiment_score', 0)
            sentiment_emoji = "😊" if sentiment > 0.2 else "😔" if sentiment < -0.2 else "😐"
            report_lines.append(f"情感：{sentiment_emoji} {sentiment:.2f}")
            
            # 讨论热度
            heat = result.get('discussion_heat', 5)
            heat_emoji = "🔥" if heat >= 8 else "🌡️" if heat >= 6 else "❄️"
            report_lines.append(f"热度：{heat_emoji} {heat}/10")
            
            # 话题详情（按热度排序）
            topics = result.get('topics', [])
            if topics:
                topic_details = []
                for topic in topics[:5]:  # 显示前5个话题（增加显示数量）
                    if isinstance(topic, dict):
                        # 如果是字典格式
                        topic_name = topic.get('topic', '未知话题')
                        keywords = topic.get('keywords', [])
                        if keywords:
                            # 显示更多关键词，最多5个
                            topic_detail = f"{topic_name}：{', '.join(keywords[:5])}"
                        else:
                            topic_detail = topic_name
                    else:
                        # 如果是字符串格式
                        topic_detail = str(topic)
                    topic_details.append(topic_detail)

                if topic_details:
                    # 如果话题详情太长，分行显示
                    combined_details = ' | '.join(topic_details)
                    if len(combined_details) > 100:
                        report_lines.append("话题详情：")
                        for idx, detail in enumerate(topic_details, 1):
                            report_lines.append(f"  {idx}. {detail}")
                    else:
                        report_lines.append(f"话题详情：{combined_details}")

                # 如果还有更多话题，显示提示
                if len(topics) > 5:
                    report_lines.append(f"  ...还有{len(topics) - 5}个话题")

            # 关键信息
            key_info = result.get('key_information', [])
            if key_info:
                # 显示所有关键信息，不截断
                if len(key_info) == 1:
                    report_lines.append(f"关键：{key_info[0]}")
                else:
                    report_lines.append("关键信息：")
                    for idx, info in enumerate(key_info[:3], 1):  # 最多显示3条
                        report_lines.append(f"  {idx}. {info}")
                    if len(key_info) > 3:
                        report_lines.append(f"  ...还有{len(key_info) - 3}条信息")

            # 深度分析结果
            deep_analysis = result.get('deep_analysis', {})
            if deep_analysis:
                self._add_deep_analysis_summary(report_lines, deep_analysis)

            report_lines.append("")

        # 添加建议
        recommendations = self._generate_recommendations(analysis_results)
        if recommendations:
            report_lines.append("💡 建议")
            for rec in recommendations[:5]:  # 显示前5个建议（增加数量）
                report_lines.append(f"• {rec}")
            if len(recommendations) > 5:
                report_lines.append(f"• ...还有{len(recommendations) - 5}条建议")
        
        return "\n".join(report_lines)
    
    def _get_priority_score(self, result: Dict[str, Any]) -> float:
        """计算分析结果的优先级评分"""
        score = 0.0
        
        # 基于话题分类的权重
        category_weights = {
            'stock': 3.0,
            'news': 2.5,
            'link': 2.0,
            'philosophy': 1.5,
            'other': 1.0
        }
        
        category = result.get('topic_category', 'other')
        score += category_weights.get(category, 1.0)
        
        # 基于讨论热度
        heat = result.get('discussion_heat', 5)
        score += heat * 0.3
        
        # 基于消息数量
        message_count = result.get('message_count', 0)
        score += min(message_count * 0.1, 2.0)
        
        # 基于情感强度
        sentiment_score = abs(result.get('sentiment_score', 0))
        score += sentiment_score * 2.0
        
        return score
    
    def _get_category_emoji(self, category: str) -> str:
        """获取话题分类对应的emoji"""
        emoji_map = {
            'stock': '📈',
            'news': '📰',
            'link': '🔗',
            'philosophy': '💭',
            'other': '💬'
        }
        return emoji_map.get(category, '💬')
    
    def _add_deep_analysis_summary(self, report_lines: List[str],
                                 deep_analysis: Dict[str, Any]):
        """添加深度分析摘要"""
        # 股票分析
        if 'stock_analysis' in deep_analysis:
            stock_data = deep_analysis['stock_analysis']
            if stock_data.get('stocks'):
                avg_score = stock_data.get('overall_analysis', {}).get('average_investment_score', 0)
                report_lines.append(f"投资评分：⭐ {avg_score}/10")

                # 显示具体股票信息
                stocks = stock_data.get('stocks', [])[:3]  # 显示前3只股票
                if stocks:
                    for stock in stocks:
                        stock_name = stock.get('name', '未知股票')
                        stock_score = stock.get('investment_score', 0)
                        report_lines.append(f"  📈 {stock_name}: {stock_score}/10")

        # 新闻分析
        if 'news_analysis' in deep_analysis:
            news_data = deep_analysis['news_analysis']
            events = news_data.get('events', [])
            if events:
                report_lines.append(f"相关事件：📅 {len(events)} 个")
                # 显示重要事件
                for event in events[:2]:  # 显示前2个事件
                    event_title = event.get('title', '未知事件')
                    if len(event_title) > 30:
                        event_title = event_title[:30] + "..."
                    report_lines.append(f"  📰 {event_title}")

        # 链接分析
        if 'link_analysis' in deep_analysis:
            link_data = deep_analysis['link_analysis']
            links = link_data.get('links', [])
            if links:
                report_lines.append(f"分享链接：🔗 {len(links)} 个")
                # 显示链接类型统计
                link_types = {}
                for link in links:
                    link_type = link.get('type', '其他')
                    link_types[link_type] = link_types.get(link_type, 0) + 1

                if link_types:
                    type_summary = ', '.join([f"{t}({c})" for t, c in link_types.items()])
                    report_lines.append(f"  类型: {type_summary}")
    
    def _generate_recommendations(self, analysis_results: List[Dict[str, Any]]) -> List[str]:
        """生成建议"""
        recommendations = []
        
        # 统计话题分类
        categories = {}
        for result in analysis_results:
            category = result.get('topic_category', 'other')
            categories[category] = categories.get(category, 0) + 1
        
        # 基于话题分布生成建议
        if categories.get('stock', 0) > 0:
            recommendations.append("发现股票讨论，建议关注市场动态")
        
        if categories.get('news', 0) > 0:
            recommendations.append("发现时事讨论，建议关注相关影响")
        
        # 基于情感分析生成建议
        negative_count = sum(1 for result in analysis_results 
                           if result.get('sentiment_score', 0) < -0.3)
        
        if negative_count > len(analysis_results) * 0.5:
            recommendations.append("整体情感偏消极，建议关注群组氛围")
        
        return recommendations
    
    def _send_message(self, content: str) -> bool:
        """
        发送文本消息到企业微信

        Args:
            content: 消息内容

        Returns:
            发送是否成功
        """
        # 企业微信文本消息长度限制为2048字符
        max_length = 2048

        if len(content) <= max_length:
            # 消息长度在限制内，直接发送
            message = {
                "msgtype": "text",
                "text": {
                    "content": content
                }
            }
            return self._send_message_raw(message)
        else:
            # 消息过长，分割发送
            logger.info(f"消息长度{len(content)}超过限制，将分割发送")
            return self._send_long_message(content, max_length)

    def _send_long_message(self, content: str, max_length: int) -> bool:
        """
        发送长消息（分割发送）

        Args:
            content: 消息内容
            max_length: 单条消息最大长度

        Returns:
            发送是否成功
        """
        lines = content.split('\n')
        current_message = ""
        message_count = 0
        all_success = True

        for line in lines:
            # 检查添加这一行后是否会超过长度限制
            test_message = current_message + ("\n" if current_message else "") + line

            if len(test_message) <= max_length:
                current_message = test_message
            else:
                # 发送当前消息
                if current_message:
                    message_count += 1
                    header = f"📊 分析报告 ({message_count}/多条)\n\n" if message_count == 1 else ""
                    message = {
                        "msgtype": "text",
                        "text": {
                            "content": header + current_message
                        }
                    }
                    success = self._send_message_raw(message)
                    all_success = all_success and success

                    if success:
                        # 等待一下再发送下一条
                        time.sleep(1)

                # 开始新消息
                current_message = line

        # 发送最后一条消息
        if current_message:
            message_count += 1
            header = f"📊 分析报告 ({message_count}/完)\n\n" if message_count > 1 else ""
            message = {
                "msgtype": "text",
                "text": {
                    "content": header + current_message
                }
            }
            success = self._send_message_raw(message)
            all_success = all_success and success

        return all_success

    def _send_message_raw(self, message: Dict[str, Any]) -> bool:
        """
        发送原始消息到企业微信

        Args:
            message: 消息对象

        Returns:
            发送是否成功
        """
        try:
            # 限流控制：确保两次发送间隔至少1秒
            current_time = time.time()
            time_since_last_send = current_time - self.last_send_time

            if time_since_last_send < self.rate_limit_interval:
                sleep_time = self.rate_limit_interval - time_since_last_send
                logger.debug(f"限流等待 {sleep_time:.2f} 秒")
                time.sleep(sleep_time)

            response = requests.post(
                self.webhook_url,
                json=message,
                timeout=10
            )

            # 更新最后发送时间
            self.last_send_time = time.time()

            if response.status_code == 200:
                result = response.json()
                if result.get('errcode') == 0:
                    logger.info("企业微信消息发送成功")
                    return True
                else:
                    logger.error(f"企业微信消息发送失败: {result}")
                    return False
            else:
                logger.error(f"企业微信API请求失败: {response.status_code}")
                return False

        except Exception as e:
            logger.error(f"发送企业微信消息异常: {e}")
            return False
    
    def test_connection(self) -> bool:
        """
        测试企业微信连接

        Returns:
            连接是否成功
        """
        test_message = f"🔧 系统测试消息\n时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n状态：连接正常"
        return self._send_message(test_message)

    def _is_duplicate_message(self, message_content: str) -> bool:
        """
        检查是否为重复消息

        Args:
            message_content: 消息内容

        Returns:
            是否为重复消息
        """
        import hashlib

        # 生成消息内容的哈希值（排除时间戳部分）
        # 只对报告内容部分进行哈希，忽略时间戳
        content_lines = message_content.split('\n')
        if len(content_lines) > 2:
            # 跳过包含时间的行，只对实际内容进行哈希
            content_for_hash = '\n'.join(content_lines[2:])
        else:
            content_for_hash = message_content

        message_hash = hashlib.md5(content_for_hash.encode('utf-8')).hexdigest()

        # 清理过期的消息记录
        current_time = time.time()
        expired_hashes = [h for h, t in self.recent_messages.items()
                         if current_time - t > self.dedup_window]
        for h in expired_hashes:
            del self.recent_messages[h]

        # 检查是否为重复消息
        return message_hash in self.recent_messages

    def _record_message(self, message_content: str):
        """
        记录已发送的消息

        Args:
            message_content: 消息内容
        """
        import hashlib

        # 生成消息内容的哈希值
        content_lines = message_content.split('\n')
        if len(content_lines) > 2:
            content_for_hash = '\n'.join(content_lines[2:])
        else:
            content_for_hash = message_content

        message_hash = hashlib.md5(content_for_hash.encode('utf-8')).hexdigest()
        self.recent_messages[message_hash] = time.time()
