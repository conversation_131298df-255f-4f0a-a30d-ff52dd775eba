#!/usr/bin/env python3
"""
测试完整分析报告和词云发送功能
"""

import sys
import os
import time
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_complete_analysis_report():
    """测试完整分析报告（不截断群组）"""
    print("🧪 测试完整分析报告显示...")
    
    try:
        from notification.wechat_notifier import WechatNotifier
        
        notifier = WechatNotifier()
        
        # 模拟多个群组的分析结果
        analysis_results = []
        for i in range(8):  # 创建8个群组的结果
            result = {
                'group_id': f'test_group_{i+1}',
                'main_topic': f'测试群组{i+1}的主要话题',
                'sentiment_score': 0.1 * (i % 10),
                'discussion_heat': 5.0 + (i % 5),
                'topic_category': ['stock', 'news', 'link', 'philosophy', 'other'][i % 5],
                'topics': [f'话题{i+1}A', f'话题{i+1}B'],
                'key_information': [f'群组{i+1}的关键信息'],
                'message_count': 10 + i * 5
            }
            analysis_results.append(result)
        
        summary = {
            'total_groups': 8,
            'total_messages': 300,
            'time_window_hours': 1
        }
        
        # 格式化报告
        report_content = notifier._format_analysis_report(analysis_results, summary)
        
        print("生成的完整报告内容:")
        print("=" * 60)
        print(report_content)
        print("=" * 60)
        
        # 检查是否显示了所有群组
        group_count = report_content.count("🔍 群组分析")
        print(f"报告中包含的群组数量: {group_count}")
        
        if group_count == 8:
            print("✅ 所有群组都显示在报告中")
        else:
            print(f"❌ 只显示了{group_count}个群组，应该显示8个")
            return False
        
        # 检查是否有"还有n个群组"的提示
        if "还有" in report_content and "个群组的分析结果" in report_content:
            print("❌ 仍然有群组数量限制提示")
            return False
        else:
            print("✅ 没有群组数量限制提示")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_wordcloud_notification_config():
    """测试词云通知配置"""
    print("\n🧪 测试词云通知配置...")
    
    try:
        from notification.wechat_notifier import WechatNotifier
        
        notifier = WechatNotifier()
        
        print(f"企业微信通知器状态:")
        print(f"  enabled: {notifier.enabled}")
        print(f"  webhook_url: {notifier.webhook_url[:50] if notifier.webhook_url else 'None'}...")
        
        if not notifier.enabled:
            print("❌ 企业微信通知未启用，这是词云不发送的原因")
            return False
        
        if not notifier.webhook_url:
            print("❌ 企业微信Webhook URL未配置")
            return False
        
        print("✅ 企业微信通知配置正常")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_wordcloud_generation():
    """测试词云生成功能"""
    print("\n🧪 测试词云生成功能...")
    
    try:
        from analysis.wordcloud_analyzer import WordCloudAnalyzer
        
        analyzer = WordCloudAnalyzer()
        
        # 模拟消息数据
        messages = [
            "这是一条测试消息，包含一些关键词",
            "股票市场今天表现不错，投资者情绪乐观",
            "科技发展迅速，人工智能技术不断进步",
            "经济政策调整，对市场产生积极影响",
            "投资需要谨慎，风险控制很重要"
        ]
        
        print("开始生成词云...")
        result = analyzer.generate_wordcloud(messages, "test_group")
        
        print(f"词云生成结果:")
        print(f"  success: {result.get('success')}")
        print(f"  word_count: {result.get('word_count', 0)}")
        print(f"  has_base64: {'base64' in result}")
        print(f"  has_md5: {'md5' in result}")
        print(f"  error: {result.get('error', 'None')}")
        
        if result.get('success'):
            print("✅ 词云生成成功")
            
            # 检查必要字段
            if 'base64' in result and 'md5' in result:
                print("✅ 词云包含必要的图片数据")
                return True
            else:
                print("❌ 词云缺少图片数据")
                return False
        else:
            print(f"❌ 词云生成失败: {result.get('error')}")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_wordcloud_sending():
    """测试词云发送功能"""
    print("\n🧪 测试词云发送功能...")
    
    try:
        from notification.wechat_notifier import WechatNotifier
        
        notifier = WechatNotifier()
        
        # 模拟词云结果
        wordcloud_result = {
            'success': True,
            'base64': 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',  # 1x1像素的透明PNG
            'md5': 'test_md5_hash',
            'word_count': 50,
            'top_words': [('测试', 10), ('词云', 8), ('发送', 6)]
        }
        
        # 记录发送次数
        send_count = 0
        original_send_raw = notifier._send_message_raw
        
        def mock_send_raw(message):
            nonlocal send_count
            send_count += 1
            print(f"模拟发送第{send_count}条消息:")
            print(f"  类型: {message.get('msgtype')}")
            if message.get('msgtype') == 'image':
                print(f"  图片MD5: {message.get('image', {}).get('md5')}")
            elif message.get('msgtype') == 'text':
                content = message.get('text', {}).get('content', '')
                print(f"  文字内容: {content[:50]}...")
            return True
        
        notifier._send_message_raw = mock_send_raw
        
        print("测试发送词云...")
        result = notifier.send_wordcloud_image('test_group', wordcloud_result)
        
        # 恢复原始方法
        notifier._send_message_raw = original_send_raw
        
        print(f"发送结果: {result}")
        print(f"总发送次数: {send_count}")
        
        if result and send_count >= 1:
            print("✅ 词云发送功能正常")
            if send_count == 2:
                print("✅ 发送了图片和文字说明")
            return True
        else:
            print("❌ 词云发送失败")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_scheduled_task_wordcloud():
    """测试定时任务的词云发送"""
    print("\n🧪 测试定时任务的词云发送...")
    
    try:
        from web.task_manager import AsyncTaskManager
        from notification.wechat_notifier import WechatNotifier
        
        # 记录词云发送次数
        wordcloud_send_count = 0
        original_send_wordcloud = WechatNotifier.send_wordcloud_image
        
        def mock_send_wordcloud(self, group_id, wordcloud_result):
            nonlocal wordcloud_send_count
            wordcloud_send_count += 1
            print(f"📊 第{wordcloud_send_count}次调用send_wordcloud_image")
            print(f"   群组: {group_id}")
            print(f"   词云成功: {wordcloud_result.get('success')}")
            return True
        
        WechatNotifier.send_wordcloud_image = mock_send_wordcloud
        
        task_manager = AsyncTaskManager()
        
        print("创建带词云发送的分析任务...")
        task_id = task_manager.create_analysis_task(
            hours=24,  # 使用24小时确保有数据
            sync_data=False,
            generate_wordcloud=True,
            send_wordcloud_notification=True,  # 启用词云发送
            send_analysis_report=False
        )
        
        # 等待任务完成
        print("等待任务完成...")
        for i in range(60):
            task_info = task_manager.get_task_info(task_id)
            if task_info and task_info.status.name in ['COMPLETED', 'FAILED']:
                print(f"任务状态: {task_info.status.name}")
                break
            time.sleep(1)
        
        # 恢复原始方法
        WechatNotifier.send_wordcloud_image = original_send_wordcloud
        
        print(f"词云发送调用次数: {wordcloud_send_count}")
        
        if wordcloud_send_count > 0:
            print("✅ 定时任务成功调用了词云发送")
            return True
        else:
            print("❌ 定时任务没有调用词云发送")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试完整分析报告和词云发送功能...")
    print("=" * 60)
    
    tests = [
        ("完整分析报告测试", test_complete_analysis_report),
        ("词云通知配置测试", test_wordcloud_notification_config),
        ("词云生成功能测试", test_wordcloud_generation),
        ("词云发送功能测试", test_wordcloud_sending),
        ("定时任务词云发送测试", test_scheduled_task_wordcloud)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            print(f"\n📋 测试: {test_name}")
            if test_func():
                passed += 1
                print(f"   ✅ {test_name} - 通过")
            else:
                print(f"   ❌ {test_name} - 失败")
        except Exception as e:
            print(f"   ❌ {test_name} - 执行失败: {e}")
    
    print("\n" + "=" * 60)
    print(f"🎯 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有功能测试通过！")
        print("\n✅ 修复总结:")
        print("1. 分析报告现在显示所有群组，不再截断")
        print("2. 词云发送功能正常工作")
        print("3. 定时任务可以正确发送词云")
    else:
        print("⚠️ 部分测试失败，需要进一步调查")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
