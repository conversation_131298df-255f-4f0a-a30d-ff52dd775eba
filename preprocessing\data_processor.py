"""
数据处理器

负责数据的获取、预处理和格式化。
"""

from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from loguru import logger

from database.db_manager import DatabaseManager
from database.models import MessageData, GroupInfo
from .message_filter import MessageFilter
from config import get_config


class DataProcessor:
    """数据处理器类"""
    
    def __init__(self):
        """初始化数据处理器"""
        self.config = get_config()
        self.db_manager = DatabaseManager()
        self.message_filter = MessageFilter()
        
        # 获取配置参数
        self.analysis_config = self.config.get_analysis_config()
        self.time_window_hours = self.analysis_config.get('time_window_hours', 1)
        self.message_types = self.analysis_config.get('message_types', [1])
    
    def get_recent_group_messages(self, hours: Optional[int] = None) -> Dict[str, List[MessageData]]:
        """
        获取最近指定小时内的群组消息
        
        Args:
            hours: 时间窗口（小时），默认使用配置中的值
            
        Returns:
            按群组分组的消息字典
        """
        if hours is None:
            hours = self.time_window_hours
        
        logger.info(f"开始获取最近 {hours} 小时内的消息")
        
        try:
            # 从数据库获取原始消息
            raw_messages = self.db_manager.get_recent_messages(
                hours=hours,
                message_types=self.message_types
            )
            
            if not raw_messages:
                logger.warning("未获取到任何消息")
                return {}
            
            # 处理每个群组的消息
            processed_messages = {}
            total_raw_count = 0
            total_processed_count = 0
            
            for group_id, messages in raw_messages.items():
                total_raw_count += len(messages)
                
                # 过滤和清洗消息
                filtered_messages = self.message_filter.filter_messages(messages)
                
                if filtered_messages:
                    # 清洗消息内容
                    for message in filtered_messages:
                        message.str_content = self.message_filter.clean_message_content(
                            message.str_content
                        )
                    
                    processed_messages[group_id] = filtered_messages
                    total_processed_count += len(filtered_messages)
                    
                    logger.debug(f"群组 {group_id}: {len(messages)} -> {len(filtered_messages)} 条消息")
            
            logger.info(f"消息处理完成: {total_raw_count} -> {total_processed_count} 条消息，{len(processed_messages)} 个群组")
            
            # 更新群组信息
            self._update_group_info(processed_messages)
            
            return processed_messages
            
        except Exception as e:
            logger.error(f"获取群组消息时发生错误: {e}")
            raise
    
    def _update_group_info(self, messages_by_group: Dict[str, List[MessageData]]):
        """更新群组信息"""
        for group_id, messages in messages_by_group.items():
            if not messages:
                continue
            
            # 获取最后一条消息的时间
            last_message_time = max(msg.create_datetime for msg in messages)
            
            # 创建或更新群组信息
            group_info = GroupInfo(
                group_id=group_id,
                member_count=len(set(msg.talker_id for msg in messages)),
                last_message_time=last_message_time,
                is_active=True
            )
            
            try:
                self.db_manager.update_group_info(group_info)
            except Exception as e:
                logger.warning(f"更新群组信息失败 {group_id}: {e}")
    
    def prepare_analysis_data(self, group_id: str, messages: List[MessageData]) -> Dict[str, Any]:
        """
        为分析准备数据
        
        Args:
            group_id: 群组ID
            messages: 消息列表
            
        Returns:
            分析数据字典
        """
        if not messages:
            return {
                'group_id': group_id,
                'message_count': 0,
                'messages': [],
                'time_range': None,
                'participants': [],
                'keywords': [],
                'message_intents': {}
            }
        
        # 按时间排序
        sorted_messages = sorted(messages, key=lambda m: m.create_datetime)
        
        # 计算时间范围
        time_range = {
            'start': sorted_messages[0].create_datetime,
            'end': sorted_messages[-1].create_datetime,
            'duration_minutes': (sorted_messages[-1].create_datetime - sorted_messages[0].create_datetime).total_seconds() / 60
        }
        
        # 统计参与者
        participants = list(set(msg.talker_id for msg in messages))
        
        # 提取所有关键词
        all_keywords = []
        for message in messages:
            keywords = self.message_filter.extract_keywords(message.str_content)
            all_keywords.extend(keywords)
        
        # 统计关键词频率
        keyword_freq = {}
        for keyword in all_keywords:
            keyword_freq[keyword] = keyword_freq.get(keyword, 0) + 1
        
        # 按频率排序关键词
        sorted_keywords = sorted(keyword_freq.items(), key=lambda x: x[1], reverse=True)[:20]
        
        # 统计消息意图
        intent_stats = {}
        for message in messages:
            intent = self.message_filter.detect_message_intent(message.str_content)
            intent_stats[intent] = intent_stats.get(intent, 0) + 1
        
        # 构建消息文本列表（用于AI分析）
        message_texts = []
        for message in sorted_messages:
            message_texts.append({
                'time': message.create_datetime.strftime('%H:%M:%S'),
                'content': message.str_content,
                'sender_id': message.talker_id
            })
        
        return {
            'group_id': group_id,
            'message_count': len(messages),
            'messages': message_texts,
            'time_range': time_range,
            'participants': participants,
            'keywords': sorted_keywords,
            'message_intents': intent_stats,
            'raw_messages': [msg.to_dict() for msg in messages]
        }
    
    def format_messages_for_ai(self, analysis_data: Dict[str, Any]) -> str:
        """
        将消息格式化为适合AI分析的文本
        
        Args:
            analysis_data: 分析数据
            
        Returns:
            格式化后的文本
        """
        if not analysis_data['messages']:
            return "没有消息内容"
        
        # 构建上下文信息
        context_info = [
            f"群组ID: {analysis_data['group_id']}",
            f"消息数量: {analysis_data['message_count']}",
            f"参与人数: {len(analysis_data['participants'])}",
            f"时间范围: {analysis_data['time_range']['start'].strftime('%Y-%m-%d %H:%M:%S')} - {analysis_data['time_range']['end'].strftime('%Y-%m-%d %H:%M:%S')}",
            f"持续时间: {analysis_data['time_range']['duration_minutes']:.1f} 分钟"
        ]
        
        # 添加关键词信息
        if analysis_data['keywords']:
            top_keywords = [f"{kw}({freq})" for kw, freq in analysis_data['keywords'][:10]]
            context_info.append(f"高频关键词: {', '.join(top_keywords)}")
        
        # 添加消息意图统计
        if analysis_data['message_intents']:
            intent_info = [f"{intent}({count})" for intent, count in analysis_data['message_intents'].items()]
            context_info.append(f"消息类型分布: {', '.join(intent_info)}")
        
        # 构建消息内容
        message_lines = []
        for msg in analysis_data['messages']:
            message_lines.append(f"[{msg['time']}] {msg['content']}")
        
        # 组合最终文本
        formatted_text = "\n".join([
            "=== 群聊分析上下文 ===",
            "\n".join(context_info),
            "",
            "=== 消息内容 ===",
            "\n".join(message_lines)
        ])
        
        return formatted_text
    
    def get_analysis_summary(self, messages_by_group: Dict[str, List[MessageData]]) -> Dict[str, Any]:
        """
        获取分析摘要信息
        
        Args:
            messages_by_group: 按群组分组的消息
            
        Returns:
            摘要信息字典
        """
        total_messages = sum(len(messages) for messages in messages_by_group.values())
        total_groups = len(messages_by_group)
        
        # 统计最活跃的群组
        group_activity = []
        for group_id, messages in messages_by_group.items():
            if messages:
                group_activity.append({
                    'group_id': group_id,
                    'message_count': len(messages),
                    'participant_count': len(set(msg.talker_id for msg in messages)),
                    'time_span': (max(msg.create_datetime for msg in messages) - 
                                min(msg.create_datetime for msg in messages)).total_seconds() / 60
                })
        
        # 按消息数量排序
        group_activity.sort(key=lambda x: x['message_count'], reverse=True)
        
        return {
            'total_messages': total_messages,
            'total_groups': total_groups,
            'most_active_groups': group_activity[:5],
            'analysis_time': datetime.now().isoformat(),
            'time_window_hours': self.time_window_hours
        }
