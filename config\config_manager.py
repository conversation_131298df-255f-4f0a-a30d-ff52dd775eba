"""
配置管理器

负责加载和管理系统配置，支持YAML配置文件和环境变量。
"""

import os
import yaml
from typing import Dict, Any, Optional
from pathlib import Path
from dotenv import load_dotenv


class ConfigManager:
    """配置管理器类"""
    
    def __init__(self, config_path: str = "config/config.yaml"):
        """
        初始化配置管理器
        
        Args:
            config_path: 配置文件路径
        """
        self.config_path = config_path
        self.config: Dict[str, Any] = {}
        self._load_config()
    
    def _load_config(self) -> None:
        """加载配置文件"""
        # 加载环境变量
        load_dotenv()
        
        # 检查配置文件是否存在
        if not Path(self.config_path).exists():
            raise FileNotFoundError(f"配置文件不存在: {self.config_path}")
        
        # 加载YAML配置
        with open(self.config_path, 'r', encoding='utf-8') as f:
            self.config = yaml.safe_load(f)
        
        # 替换环境变量占位符
        self._replace_env_variables(self.config)
    
    def _replace_env_variables(self, obj: Any) -> Any:
        """递归替换配置中的环境变量占位符"""
        if isinstance(obj, dict):
            return {k: self._replace_env_variables(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._replace_env_variables(item) for item in obj]
        elif isinstance(obj, str) and obj.startswith("${") and obj.endswith("}"):
            # 提取环境变量名
            env_var = obj[2:-1]
            return os.getenv(env_var, obj)
        else:
            return obj
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值，支持点号分隔的嵌套键
        
        Args:
            key: 配置键，支持 'database.wechat_db_path' 格式
            default: 默认值
            
        Returns:
            配置值
        """
        keys = key.split('.')
        value = self.config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def get_database_config(self) -> Dict[str, str]:
        """获取数据库配置"""
        return self.get('database', {})
    
    def get_doubao_config(self) -> Dict[str, Any]:
        """获取豆包API配置"""
        return self.get('doubao_api', {})
    
    def get_wechat_notification_config(self) -> Dict[str, str]:
        """获取企业微信通知配置"""
        return self.get('wechat_notification', {})
    
    def get_analysis_config(self) -> Dict[str, Any]:
        """获取分析配置"""
        return self.get('analysis', {})
    
    def get_deep_analysis_config(self) -> Dict[str, Any]:
        """获取深度分析配置"""
        return self.get('deep_analysis', {})
    
    def get_logging_config(self) -> Dict[str, Any]:
        """获取日志配置"""
        return self.get('logging', {})
    
    def get_scheduler_config(self) -> Dict[str, Any]:
        """获取调度配置"""
        return self.get('scheduler', {})


# 全局配置实例
_config_manager: Optional[ConfigManager] = None


def get_config() -> ConfigManager:
    """
    获取全局配置管理器实例
    
    Returns:
        ConfigManager实例
    """
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigManager()
    return _config_manager
