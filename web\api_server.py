"""
Web API服务器

基于FastAPI的Web管理界面后端服务。
"""

import os
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from pathlib import Path

from fastapi import FastAPI, HTTPException, Query, BackgroundTasks
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.responses import HTMLResponse, JSONResponse, FileResponse, RedirectResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel

from database.db_manager import DatabaseManager
from database.models import GroupInfo
from main import WechatAnalysisSystem
from analysis.wordcloud_analyzer import WordCloudAnalyzer
from web.task_manager import task_manager, TaskStatus
from web.schedule_manager import schedule_manager
from loguru import logger


# 数据模型
class AnalysisRequest(BaseModel):
    """分析请求模型"""
    hours: int = 1
    group_ids: Optional[List[str]] = None
    sync_data: bool = True
    generate_wordcloud: bool = True
    send_wordcloud_notification: bool = False
    send_analysis_report: bool = False


class GroupConfigRequest(BaseModel):
    """群组配置请求模型"""
    group_id: str
    group_name: Optional[str] = None
    need_analysis: bool = True


class ScheduleConfigRequest(BaseModel):
    """定时任务配置请求模型"""
    interval_minutes: int = 60
    send_analysis_report: bool = True
    send_wordcloud: bool = True
    silent_start: str = "23:00"
    silent_end: str = "07:00"
    enabled: bool = False


def create_app() -> FastAPI:
    """创建FastAPI应用"""
    app = FastAPI(
        title="微信群聊内容智能分析系统",
        description="基于AI的微信群聊内容分析和管理平台",
        version="1.0.0",
        docs_url="/docs",
        redoc_url="/redoc"
    )
    
    # 添加CORS中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # 初始化组件
    db_manager = DatabaseManager()
    analysis_system = WechatAnalysisSystem()
    wordcloud_analyzer = WordCloudAnalyzer()
    
    # 静态文件和模板
    static_dir = Path(__file__).parent / "static"
    templates_dir = Path(__file__).parent / "templates"
    
    if static_dir.exists():
        app.mount("/static", StaticFiles(directory=str(static_dir)), name="static")
    
    if templates_dir.exists():
        templates = Jinja2Templates(directory=str(templates_dir))
    
    @app.get("/", response_class=HTMLResponse)
    async def index():
        """主页"""
        return """
        <!DOCTYPE html>
        <html>
        <head>
            <title>微信群聊分析系统</title>
            <meta charset="utf-8">
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
                .container { max-width: 1400px; margin: 0 auto; }
                .header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; background: white; padding: 15px 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
                .header-left h1 { margin: 0; font-size: 24px; }
                .header-left p { margin: 5px 0 0 0; font-size: 14px; color: #666; }
                .nav { display: flex; gap: 15px; margin-bottom: 20px; }
                .nav a { padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; }
                .nav a:hover { background: #0056b3; }
                .analysis-section { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); margin-bottom: 20px; }
                .analysis-controls { display: flex; gap: 15px; align-items: center; margin-bottom: 20px; flex-wrap: wrap; }
                .btn { padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; font-size: 14px; }
                .btn-primary { background: #007bff; color: white; }
                .btn-success { background: #28a745; color: white; }
                .btn-secondary { background: #6c757d; color: white; }
                .btn:hover { opacity: 0.9; }
                .btn:disabled { opacity: 0.6; cursor: not-allowed; }
                .form-group { display: flex; align-items: center; gap: 10px; }
                .form-group label { font-weight: bold; }
                .form-group select, .form-group input { padding: 5px 10px; border: 1px solid #ddd; border-radius: 4px; }
                .result-item { border: 1px solid #ddd; padding: 20px; border-radius: 8px; margin-bottom: 20px; background: #f9f9f9; display: flex; gap: 20px; }
                .result-content { flex: 1; }
                .result-wordcloud { flex-shrink: 0; }
                .result-header { font-weight: bold; color: #007bff; margin-bottom: 15px; font-size: 16px; }
                .wordcloud-preview { width: 200px; height: 150px; object-fit: cover; border-radius: 8px; cursor: pointer; border: 1px solid #ddd; }
                .wordcloud-preview:hover { opacity: 0.8; }
                .key-info-section { margin-top: 15px; }
                .key-info-category { margin-bottom: 10px; }
                .key-info-title { font-weight: bold; color: #333; margin-bottom: 5px; }
                .key-info-items { margin-left: 15px; color: #666; }
                .loading { text-align: center; padding: 40px; }
                .task-status { background: #e9ecef; padding: 15px; border-radius: 8px; margin: 15px 0; }
                .progress-bar { width: 100%; height: 20px; background: #e9ecef; border-radius: 10px; overflow: hidden; margin: 10px 0; }
                .progress-fill { height: 100%; background: #007bff; transition: width 0.3s ease; }
                .logs { background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 4px; padding: 10px; max-height: 200px; overflow-y: auto; font-family: monospace; font-size: 12px; }
                .modal { display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); }
                .modal-content { background: white; margin: 5% auto; padding: 20px; width: 80%; max-width: 800px; border-radius: 8px; }
                .close { color: #aaa; float: right; font-size: 28px; font-weight: bold; cursor: pointer; }
                .close:hover { color: black; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <div class="header-left">
                        <h1>🤖 微信群聊内容智能分析系统</h1>
                        <p>基于AI的群聊内容分析和管理平台</p>
                    </div>
                    <div class="header-right">
                        <button class="btn btn-success" onclick="startAnalysis()" id="startBtn">开始分析</button>
                        <button class="btn btn-secondary" onclick="openTaskModal()" id="taskBtn" style="display: none; margin-left: 10px;">查看任务详情</button>
                        <button class="btn btn-primary" onclick="openScheduleModal()" id="scheduleBtn" style="margin-left: 10px;">定时任务</button>
                    </div>
                </div>

                <div class="nav">
                    <a href="/groups">群组管理</a>
                    <a href="/analysis">分析报告</a>
                    <a href="/wordcloud">词云分析</a>
                    <a href="/chat">聊天查询</a>
                    <a href="/api/docs">API文档</a>
                </div>

                <!-- 定时任务配置模态框 -->
                <div id="scheduleModal" class="modal">
                    <div class="modal-content">
                        <span class="close" onclick="closeScheduleModal()">&times;</span>
                        <h3>定时任务配置</h3>
                        <div class="analysis-controls">
                            <div class="form-group">
                                <label>发送时间间隔:</label>
                                <select id="scheduleInterval">
                                    <option value="30">每30分钟</option>
                                    <option value="60" selected>每1小时</option>
                                    <option value="120">每2小时</option>
                                    <option value="360">每6小时</option>
                                    <option value="720">每12小时</option>
                                    <option value="1440">每24小时</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label>发送类型:</label>
                                <div style="margin-left: 20px;">
                                    <label><input type="checkbox" id="scheduleAnalysisReport" checked> 分析报告</label><br>
                                    <label><input type="checkbox" id="scheduleWordcloud" checked> 词云图片</label>
                                </div>
                            </div>

                            <div class="form-group">
                                <label>静默时间段:</label>
                                <div style="margin-left: 20px;">
                                    <label>开始时间: <input type="time" id="silentStart" value="23:00"></label><br>
                                    <label>结束时间: <input type="time" id="silentEnd" value="07:00"></label>
                                </div>
                            </div>

                            <div class="form-group">
                                <label><input type="checkbox" id="enableSchedule"> 启用定时任务</label>
                            </div>

                            <button class="btn btn-success" onclick="saveScheduleConfig()">保存配置</button>
                            <button class="btn btn-secondary" onclick="closeScheduleModal()">取消</button>
                        </div>
                    </div>
                </div>

                <!-- 分析配置模态框 -->
                <div id="analysisModal" class="modal">
                    <div class="modal-content">
                        <span class="close" onclick="closeAnalysisModal()">&times;</span>
                        <h3>分析配置</h3>
                        <div class="analysis-controls">
                            <div class="form-group">
                                <label>时间窗口:</label>
                                <select id="hoursSelect">
                                    <option value="1" selected>最近1小时</option>
                                    <option value="6">最近6小时</option>
                                    <option value="24">最近24小时</option>
                                    <option value="72">最近3天</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label><input type="checkbox" id="syncData" checked> 同步数据</label>
                            </div>

                            <div class="form-group">
                                <label><input type="checkbox" id="generateWordcloud" checked> 生成词云</label>
                            </div>

                            <div class="form-group">
                                <label><input type="checkbox" id="sendNotification"> 发送词云通知</label>
                            </div>

                            <div class="form-group">
                                <label><input type="checkbox" id="sendAnalysisReport"> 发送分析报告</label>
                            </div>

                            <button class="btn btn-success" onclick="executeAnalysis()">确认执行</button>
                        </div>

                        <div id="taskStatus" class="task-status" style="display: none;">
                            <div><strong>任务状态:</strong> <span id="statusText">-</span></div>
                            <div><strong>当前步骤:</strong> <span id="currentStep">-</span></div>
                            <div class="progress-bar">
                                <div class="progress-fill" id="progressFill" style="width: 0%;"></div>
                            </div>
                            <div><span id="progressText">0%</span></div>
                        </div>
                    </div>
                </div>

                <div class="analysis-section">
                    <h3>📊 最新分析报告</h3>
                    <div id="loading" class="loading" style="display: none;">正在加载分析结果...</div>
                    <div id="results"></div>
                </div>
            </div>

            <!-- 任务详情模态框 -->
            <div id="taskModal" class="modal">
                <div class="modal-content">
                    <span class="close" onclick="closeTaskModal()">&times;</span>
                    <h3>任务执行详情</h3>
                    <div id="taskDetails"></div>
                    <h4>实时日志</h4>
                    <div id="taskLogs" class="logs"></div>
                </div>
            </div>

            <!-- 词云放大模态框 -->
            <div id="imageModal" class="modal">
                <div class="modal-content">
                    <span class="close" onclick="closeImageModal()">&times;</span>
                    <img id="modalImage" style="width: 100%; height: auto;">
                </div>
            </div>

            <script>
                let currentTaskId = null;
                let taskUpdateInterval = null;

                function startAnalysis() {
                    document.getElementById('analysisModal').style.display = 'block';
                }

                function closeAnalysisModal() {
                    document.getElementById('analysisModal').style.display = 'none';
                }

                async function executeAnalysis() {
                    const startBtn = document.getElementById('startBtn');
                    const taskBtn = document.getElementById('taskBtn');
                    const taskStatus = document.getElementById('taskStatus');

                    startBtn.disabled = true;
                    startBtn.textContent = '启动中...';

                    try {
                        const response = await fetch('/api/analysis/run', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                hours: parseInt(document.getElementById('hoursSelect').value),
                                sync_data: document.getElementById('syncData').checked,
                                generate_wordcloud: document.getElementById('generateWordcloud').checked,
                                send_wordcloud_notification: document.getElementById('sendNotification').checked,
                                send_analysis_report: document.getElementById('sendAnalysisReport').checked
                            })
                        });

                        const result = await response.json();
                        if (result.success) {
                            currentTaskId = result.task_id;
                            taskStatus.style.display = 'block';
                            taskBtn.style.display = 'inline-block';

                            // 关闭配置模态框
                            closeAnalysisModal();

                            // 开始轮询任务状态
                            startTaskPolling();

                            alert('分析任务已启动！');
                        } else {
                            alert('启动分析任务失败: ' + result.message);
                        }

                    } catch (error) {
                        console.error('启动分析失败:', error);
                        alert('启动分析失败: ' + error.message);
                    } finally {
                        startBtn.disabled = false;
                        startBtn.textContent = '开始分析';
                    }
                }

                function startTaskPolling() {
                    if (taskUpdateInterval) {
                        clearInterval(taskUpdateInterval);
                    }

                    taskUpdateInterval = setInterval(updateTaskStatus, 2000);
                    updateTaskStatus(); // 立即更新一次
                }

                async function updateTaskStatus() {
                    if (!currentTaskId) return;

                    try {
                        const response = await fetch(`/api/analysis/task/${currentTaskId}`);
                        const taskInfo = await response.json();

                        document.getElementById('statusText').textContent = taskInfo.status;
                        document.getElementById('currentStep').textContent = taskInfo.current_step;
                        document.getElementById('progressFill').style.width = taskInfo.progress + '%';
                        document.getElementById('progressText').textContent = taskInfo.progress + '%';

                        // 如果任务完成，停止轮询并刷新结果
                        if (taskInfo.status === 'completed' || taskInfo.status === 'failed') {
                            clearInterval(taskUpdateInterval);
                            taskUpdateInterval = null;

                            if (taskInfo.status === 'completed') {
                                setTimeout(() => {
                                    loadResults();
                                    document.getElementById('taskStatus').style.display = 'none';
                                }, 2000);
                            }
                        }

                    } catch (error) {
                        console.error('更新任务状态失败:', error);
                    }
                }

                async function loadResults() {
                    const loading = document.getElementById('loading');
                    const results = document.getElementById('results');

                    loading.style.display = 'block';
                    results.innerHTML = '';

                    try {
                        // 获取最近24小时内的分析结果，按热度排序
                        const response = await fetch('/api/analysis/results?hours=24&limit=10&sort_by=heat');
                        const data = await response.json();

                        if (data.length === 0) {
                            results.innerHTML = '<p>暂无分析结果</p>';
                        } else {
                            for (const result of data) {
                                const resultDiv = document.createElement('div');
                                resultDiv.className = 'result-item';

                                // 获取词云图片
                                let wordcloudImg = '';
                                try {
                                    const wordcloudResponse = await fetch('/api/wordcloud/list');
                                    const wordclouds = await wordcloudResponse.json();
                                    const groupWordcloud = wordclouds.find(w => w.filename.includes(result.group_id.substring(0, 10)));
                                    if (groupWordcloud) {
                                        wordcloudImg = `<img src="/api/wordcloud/image/${groupWordcloud.filename}"
                                                       class="wordcloud-preview"
                                                       onclick="showImageModal('/api/wordcloud/image/${groupWordcloud.filename}')"
                                                       alt="词云图片">`;
                                    }
                                } catch (e) {
                                    console.log('获取词云失败:', e);
                                }

                                // 处理关键信息的分类显示
                                let keyInfoHtml = '';
                                if (result.key_information && result.key_information.length > 0) {
                                    // 尝试按主题分类关键信息
                                    const mainTopic = result.main_topic || '';
                                    const keyInfos = result.key_information;

                                    if (mainTopic.includes('、') || mainTopic.includes('及') || mainTopic.includes('和')) {
                                        // 如果主题包含多个内容，尝试分类
                                        const topics = mainTopic.split(/[、及和]/);
                                        keyInfoHtml = '<div class="key-info-section">';

                                        topics.forEach((topic, index) => {
                                            if (topic.trim()) {
                                                keyInfoHtml += `
                                                    <div class="key-info-category">
                                                        <div class="key-info-title">关于${topic.trim()}：</div>
                                                        <div class="key-info-items">
                                                            ${keyInfos.slice(index * 2, (index + 1) * 2).join('；') || '暂无详细信息'}
                                                        </div>
                                                    </div>
                                                `;
                                            }
                                        });
                                        keyInfoHtml += '</div>';
                                    } else {
                                        // 简单显示
                                        keyInfoHtml = `<p><strong>关键信息:</strong> ${keyInfos.slice(0, 3).join('；')}</p>`;
                                    }
                                } else {
                                    keyInfoHtml = '<p><strong>关键信息:</strong> 无</p>';
                                }

                                resultDiv.innerHTML = `
                                    <div class="result-content">
                                        <div class="result-header">
                                            群组: ${result.group_name || result.group_id.substring(0, 30) + '...'} |
                                            话题: ${result.topic_category} |
                                            时间: ${new Date(result.analysis_time).toLocaleString()}
                                        </div>
                                        <p><strong>主要话题:</strong> ${result.main_topic}</p>
                                        <p><strong>情感评分:</strong> ${result.sentiment_score}</p>
                                        <p><strong>讨论热度:</strong> ${result.discussion_heat || result.overall_score || 'N/A'}/10</p>
                                        ${keyInfoHtml}
                                    </div>
                                    <div class="result-wordcloud">
                                        ${wordcloudImg}
                                    </div>
                                `;
                                results.appendChild(resultDiv);
                            }
                        }

                    } catch (error) {
                        console.error('加载结果失败:', error);
                        results.innerHTML = '<p>加载失败: ' + error.message + '</p>';
                    } finally {
                        loading.style.display = 'none';
                    }
                }

                function openTaskModal() {
                    document.getElementById('taskModal').style.display = 'block';
                    updateTaskDetails();
                }

                function closeTaskModal() {
                    document.getElementById('taskModal').style.display = 'none';
                }

                async function updateTaskDetails() {
                    if (!currentTaskId) return;

                    try {
                        // 获取任务状态
                        const statusResponse = await fetch(`/api/analysis/task/${currentTaskId}`);
                        const taskInfo = await statusResponse.json();

                        document.getElementById('taskDetails').innerHTML = `
                            <p><strong>任务ID:</strong> ${taskInfo.task_id}</p>
                            <p><strong>状态:</strong> ${taskInfo.status}</p>
                            <p><strong>进度:</strong> ${taskInfo.progress}%</p>
                            <p><strong>当前步骤:</strong> ${taskInfo.current_step}</p>
                            <p><strong>创建时间:</strong> ${new Date(taskInfo.created_at).toLocaleString()}</p>
                            ${taskInfo.started_at ? `<p><strong>开始时间:</strong> ${new Date(taskInfo.started_at).toLocaleString()}</p>` : ''}
                            ${taskInfo.completed_at ? `<p><strong>完成时间:</strong> ${new Date(taskInfo.completed_at).toLocaleString()}</p>` : ''}
                            ${taskInfo.error ? `<p><strong>错误:</strong> ${taskInfo.error}</p>` : ''}
                        `;

                        // 获取任务日志
                        const logsResponse = await fetch(`/api/analysis/task/${currentTaskId}/logs`);
                        const logsData = await logsResponse.json();

                        const logsDiv = document.getElementById('taskLogs');
                        logsDiv.innerHTML = logsData.logs.join('<br>');
                        logsDiv.scrollTop = logsDiv.scrollHeight;

                    } catch (error) {
                        console.error('更新任务详情失败:', error);
                    }
                }

                function showImageModal(imageSrc) {
                    document.getElementById('modalImage').src = imageSrc;
                    document.getElementById('imageModal').style.display = 'block';
                }

                function closeImageModal() {
                    document.getElementById('imageModal').style.display = 'none';
                }

                function openScheduleModal() {
                    document.getElementById('scheduleModal').style.display = 'block';
                    loadScheduleConfig();
                }

                function closeScheduleModal() {
                    document.getElementById('scheduleModal').style.display = 'none';
                }

                async function saveScheduleConfig() {
                    try {
                        const config = {
                            interval_minutes: parseInt(document.getElementById('scheduleInterval').value),
                            send_analysis_report: document.getElementById('scheduleAnalysisReport').checked,
                            send_wordcloud: document.getElementById('scheduleWordcloud').checked,
                            silent_start: document.getElementById('silentStart').value,
                            silent_end: document.getElementById('silentEnd').value,
                            enabled: document.getElementById('enableSchedule').checked
                        };

                        const response = await fetch('/api/schedule/config', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify(config)
                        });

                        const result = await response.json();
                        if (result.success) {
                            alert('定时任务配置保存成功！');
                            closeScheduleModal();
                        } else {
                            alert('保存失败: ' + result.message);
                        }

                    } catch (error) {
                        console.error('保存定时任务配置失败:', error);
                        alert('保存失败: ' + error.message);
                    }
                }

                async function loadScheduleConfig() {
                    try {
                        const response = await fetch('/api/schedule/config');
                        const config = await response.json();

                        if (config) {
                            document.getElementById('scheduleInterval').value = config.interval_minutes || 60;
                            document.getElementById('scheduleAnalysisReport').checked = config.send_analysis_report !== false;
                            document.getElementById('scheduleWordcloud').checked = config.send_wordcloud !== false;
                            document.getElementById('silentStart').value = config.silent_start || '23:00';
                            document.getElementById('silentEnd').value = config.silent_end || '07:00';
                            document.getElementById('enableSchedule').checked = config.enabled || false;
                        }

                    } catch (error) {
                        console.error('加载定时任务配置失败:', error);
                    }
                }

                // 页面加载时初始化
                loadResults();

                // 如果模态框打开，定期更新任务详情
                setInterval(() => {
                    if (document.getElementById('taskModal').style.display === 'block') {
                        updateTaskDetails();
                    }
                }, 3000);
            </script>
        </body>
        </html>
        """
    
    @app.get("/api/groups")
    async def get_groups():
        """获取群组列表"""
        try:
            groups = db_manager.get_active_groups()
            return [group.to_dict() for group in groups]
        except Exception as e:
            logger.error(f"获取群组列表失败: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    @app.post("/api/groups/config")
    async def update_group_config(request: GroupConfigRequest):
        """更新群组配置"""
        try:
            logger.info(f"更新群组配置: {request.group_id}, need_analysis: {request.need_analysis}")

            # 获取当前群组信息
            current_groups = db_manager.get_active_groups()
            current_group = None
            for group in current_groups:
                if group.group_id == request.group_id:
                    current_group = group
                    break

            # 如果群组不存在，创建新的群组信息
            if not current_group:
                current_group = GroupInfo(
                    group_id=request.group_id,
                    group_name=request.group_name or '',
                    need_analysis=request.need_analysis,
                    created_at=datetime.now()
                )
            else:
                # 更新现有群组信息
                if request.group_name is not None:
                    current_group.group_name = request.group_name
                # 直接更新need_analysis字段
                current_group.need_analysis = request.need_analysis

            # 保存群组信息
            db_manager.update_group_info(current_group)

            # 确保分析状态更新到数据库
            success = db_manager.set_group_analysis_status(
                request.group_id, request.need_analysis
            )
            if not success:
                logger.error(f"更新群组 {request.group_id} 分析状态失败")
                return {"success": False, "message": "分析状态更新失败"}

            logger.info(f"群组 {request.group_id} 配置更新成功，分析状态: {request.need_analysis}")
            return {"success": True, "message": "群组配置更新成功"}

        except Exception as e:
            logger.error(f"更新群组配置失败: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    @app.post("/api/analysis/run")
    async def run_analysis(request: AnalysisRequest):
        """运行分析任务"""
        try:
            # 创建异步分析任务
            task_id = task_manager.create_analysis_task(
                hours=request.hours,
                sync_data=request.sync_data,
                generate_wordcloud=request.generate_wordcloud,
                send_wordcloud_notification=request.send_wordcloud_notification,
                send_analysis_report=request.send_analysis_report
            )

            return {
                "success": True,
                "message": "分析任务已启动",
                "task_id": task_id
            }

        except Exception as e:
            logger.error(f"启动分析任务失败: {e}")
            raise HTTPException(status_code=500, detail=str(e))

    @app.get("/api/analysis/task/{task_id}")
    async def get_task_status(task_id: str):
        """获取任务状态"""
        try:
            task_info = task_manager.get_task_info(task_id)
            if not task_info:
                raise HTTPException(status_code=404, detail="任务不存在")

            return {
                "task_id": task_info.task_id,
                "status": task_info.status.value,
                "progress": task_info.progress,
                "current_step": task_info.current_step,
                "created_at": task_info.created_at.isoformat(),
                "started_at": task_info.started_at.isoformat() if task_info.started_at else None,
                "completed_at": task_info.completed_at.isoformat() if task_info.completed_at else None,
                "error": task_info.error
            }

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"获取任务状态失败: {e}")
            raise HTTPException(status_code=500, detail=str(e))

    @app.get("/api/analysis/task/{task_id}/logs")
    async def get_task_logs(task_id: str, last_count: int = 50):
        """获取任务日志"""
        try:
            task_info = task_manager.get_task_info(task_id)
            if not task_info:
                raise HTTPException(status_code=404, detail="任务不存在")

            logs = task_manager.get_task_logs(task_id, last_count)

            return {
                "task_id": task_id,
                "logs": logs,
                "total_logs": len(logs)
            }

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"获取任务日志失败: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    @app.get("/api/analysis/results")
    async def get_analysis_results(
        group_id: Optional[str] = Query(None),
        hours: int = Query(24),
        limit: int = Query(50),
        sort_by: Optional[str] = Query(None)
    ):
        """获取分析结果"""
        try:
            results = db_manager.get_analysis_results(
                group_id=group_id,
                hours=hours,
                limit=limit
            )

            # 如果指定按热度排序
            if sort_by == "heat":
                results.sort(key=lambda x: getattr(x, 'discussion_heat', 0), reverse=True)

            # 转换为字典并添加群组名称
            result_dicts = []
            for result in results:
                result_dict = result.to_dict()

                # 获取群组名称
                groups = db_manager.get_active_groups()
                group_name = None
                for group in groups:
                    if group.group_id == result.group_id:
                        group_name = group.group_name
                        break

                result_dict['group_name'] = group_name or result.group_id[:30] + "..."
                result_dicts.append(result_dict)

            return result_dicts

        except Exception as e:
            logger.error(f"获取分析结果失败: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    @app.get("/api/wordcloud/list")
    async def get_wordcloud_list():
        """获取词云文件列表"""
        try:
            wordcloud_dir = Path("./data/wordclouds")
            if not wordcloud_dir.exists():
                return []
            
            wordcloud_files = []
            for file_path in wordcloud_dir.glob("wordcloud_*.png"):
                stat = file_path.stat()
                wordcloud_files.append({
                    'filename': file_path.name,
                    'filepath': str(file_path),
                    'size': stat.st_size,
                    'created_time': datetime.fromtimestamp(stat.st_ctime).isoformat(),
                    'modified_time': datetime.fromtimestamp(stat.st_mtime).isoformat()
                })
            
            # 按修改时间倒序排列
            wordcloud_files.sort(key=lambda x: x['modified_time'], reverse=True)
            return wordcloud_files
            
        except Exception as e:
            logger.error(f"获取词云列表失败: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    @app.get("/api/wordcloud/image/{filename}")
    async def get_wordcloud_image(filename: str):
        """获取词云图片"""
        try:
            file_path = Path("./data/wordclouds") / filename
            
            if not file_path.exists() or not file_path.name.startswith("wordcloud_"):
                raise HTTPException(status_code=404, detail="词云文件不存在")
            
            return FileResponse(
                path=str(file_path),
                media_type="image/png",
                filename=filename
            )
            
        except Exception as e:
            logger.error(f"获取词云图片失败: {e}")
            raise HTTPException(status_code=500, detail=str(e))
    
    @app.get("/api/stats/dashboard")
    async def get_dashboard_stats():
        """获取仪表板统计数据"""
        try:
            # 获取群组统计
            groups = db_manager.get_active_groups()
            total_groups = len(groups)
            analysis_enabled_groups = sum(1 for g in groups if g.need_analysis)
            
            # 获取最近24小时的分析结果
            recent_results = db_manager.get_analysis_results(hours=24)
            
            # 统计话题分类
            topic_stats = {}
            for result in recent_results:
                category = result.topic_category
                topic_stats[category] = topic_stats.get(category, 0) + 1
            
            # 统计情感分布
            sentiment_stats = {'positive': 0, 'negative': 0, 'neutral': 0}
            for result in recent_results:
                if result.sentiment_score > 0.2:
                    sentiment_stats['positive'] += 1
                elif result.sentiment_score < -0.2:
                    sentiment_stats['negative'] += 1
                else:
                    sentiment_stats['neutral'] += 1
            
            return {
                'groups': {
                    'total': total_groups,
                    'analysis_enabled': analysis_enabled_groups,
                    'analysis_disabled': total_groups - analysis_enabled_groups
                },
                'recent_analysis': {
                    'total_results': len(recent_results),
                    'topic_distribution': topic_stats,
                    'sentiment_distribution': sentiment_stats
                },
                'system_status': {
                    'last_update': datetime.now().isoformat(),
                    'status': 'running'
                }
            }

        except Exception as e:
            logger.error(f"获取仪表板统计失败: {e}")
            raise HTTPException(status_code=500, detail=str(e))

    @app.post("/api/schedule/config")
    async def save_schedule_config(request: ScheduleConfigRequest):
        """保存定时任务配置"""
        try:
            # 保存配置到文件
            config_file = Path("./data/schedule_config.json")
            config_file.parent.mkdir(exist_ok=True)

            config_data = {
                "interval_minutes": request.interval_minutes,
                "send_analysis_report": request.send_analysis_report,
                "send_wordcloud": request.send_wordcloud,
                "silent_start": request.silent_start,
                "silent_end": request.silent_end,
                "enabled": request.enabled,
                "updated_at": datetime.now().isoformat()
            }

            import json
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, ensure_ascii=False, indent=2)

            logger.info(f"定时任务配置已保存: {config_data}")

            return {"success": True, "message": "定时任务配置保存成功"}

        except Exception as e:
            logger.error(f"保存定时任务配置失败: {e}")
            raise HTTPException(status_code=500, detail=str(e))

    @app.get("/api/schedule/config")
    async def get_schedule_config():
        """获取定时任务配置"""
        try:
            config_file = Path("./data/schedule_config.json")

            if not config_file.exists():
                # 返回默认配置
                return {
                    "interval_minutes": 60,
                    "send_analysis_report": True,
                    "send_wordcloud": True,
                    "silent_start": "23:00",
                    "silent_end": "07:00",
                    "enabled": False
                }

            import json
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)

            return config_data

        except Exception as e:
            logger.error(f"获取定时任务配置失败: {e}")
            raise HTTPException(status_code=500, detail=str(e))

    @app.get("/api/chat/messages")
    async def get_chat_messages(
        group_id: str = Query(...),
        hours: int = Query(24),
        limit: int = Query(50)
    ):
        """获取群聊消息"""
        try:
            messages = db_manager.get_recent_messages_for_group(
                group_id=group_id,
                hours=hours,
                limit=limit
            )

            return [msg.to_dict() for msg in messages]

        except Exception as e:
            logger.error(f"获取群聊消息失败: {e}")
            raise HTTPException(status_code=500, detail=str(e))

    @app.get("/groups", response_class=HTMLResponse)
    async def groups_page():
        """群组管理页面"""
        return """
        <!DOCTYPE html>
        <html>
        <head>
            <title>群组管理 - 微信群聊分析系统</title>
            <meta charset="utf-8">
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .container { max-width: 1400px; margin: 0 auto; }
                .header { margin-bottom: 30px; }
                .back-link { color: #007bff; text-decoration: none; }
                .back-link:hover { text-decoration: underline; }
                table { width: 100%; border-collapse: collapse; margin-top: 20px; }
                th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; vertical-align: top; }
                th { background-color: #f8f9fa; }
                .status-enabled { color: #28a745; font-weight: bold; }
                .status-disabled { color: #dc3545; font-weight: bold; }
                .btn { padding: 6px 12px; border: none; border-radius: 4px; cursor: pointer; margin: 2px; }
                .btn-primary { background: #007bff; color: white; }
                .btn-success { background: #28a745; color: white; }
                .btn-danger { background: #dc3545; color: white; }
                .btn-secondary { background: #6c757d; color: white; }
                .btn:hover { opacity: 0.9; }
                .loading { text-align: center; padding: 40px; }
                .group-name-input { width: 150px; padding: 4px; border: 1px solid #ddd; border-radius: 4px; }
                .recent-messages { max-width: 300px; max-height: 100px; overflow-y: auto; font-size: 12px; }
                .message-item { margin: 2px 0; padding: 4px; background: #f8f9fa; border-radius: 4px; }
                .message-time { color: #666; font-size: 10px; }
                .message-content { margin-top: 2px; }
                .group-id-cell { max-width: 200px; word-break: break-all; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <a href="/" class="back-link">← 返回首页</a>
                    <h1>📱 群组管理</h1>
                    <p>管理群组分析设置和查看群组信息</p>
                </div>

                <div id="loading" class="loading">正在加载群组数据...</div>
                <div id="content" style="display: none;">
                    <table id="groupsTable">
                        <thead>
                            <tr>
                                <th>群组ID</th>
                                <th>群组名称</th>
                                <th>分析状态</th>
                                <th>最近消息</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="groupsBody">
                        </tbody>
                    </table>
                </div>
            </div>

            <script>
                async function loadGroups() {
                    try {
                        const response = await fetch('/api/groups');
                        const groups = await response.json();

                        const tbody = document.getElementById('groupsBody');
                        tbody.innerHTML = '';

                        for (const group of groups) {
                            // 获取最近消息
                            let recentMessagesHtml = '<div>加载中...</div>';
                            try {
                                const messagesResponse = await fetch(`/api/chat/messages?group_id=${group.group_id}&hours=24&limit=10`);
                                const messages = await messagesResponse.json();

                                if (messages.length > 0) {
                                    recentMessagesHtml = messages.map(msg => `
                                        <div class="message-item">
                                            <div class="message-time">${new Date(msg.create_datetime).toLocaleString()}</div>
                                            <div class="message-content">${msg.str_content.substring(0, 50)}${msg.str_content.length > 50 ? '...' : ''}</div>
                                        </div>
                                    `).join('');
                                } else {
                                    recentMessagesHtml = '<div>暂无消息</div>';
                                }
                            } catch (e) {
                                recentMessagesHtml = '<div>加载失败</div>';
                            }

                            const row = document.createElement('tr');
                            row.innerHTML = `
                                <td class="group-id-cell">${group.group_id}</td>
                                <td>
                                    <input type="text" class="group-name-input"
                                           value="${group.group_name || ''}"
                                           placeholder="输入群组名称"
                                           onblur="updateGroupName('${group.group_id}', this.value)">
                                </td>
                                <td class="${group.need_analysis ? 'status-enabled' : 'status-disabled'}">
                                    ${group.need_analysis ? '已启用' : '已禁用'}
                                </td>
                                <td>
                                    <div class="recent-messages">
                                        ${recentMessagesHtml}
                                    </div>
                                </td>
                                <td>
                                    <button class="btn ${group.need_analysis ? 'btn-danger' : 'btn-primary'}"
                                            onclick="toggleAnalysis('${group.group_id}', ${!group.need_analysis})">
                                        ${group.need_analysis ? '禁用分析' : '启用分析'}
                                    </button>
                                </td>
                            `;
                            tbody.appendChild(row);
                        }

                        document.getElementById('loading').style.display = 'none';
                        document.getElementById('content').style.display = 'block';

                    } catch (error) {
                        console.error('加载群组失败:', error);
                        document.getElementById('loading').innerHTML = '加载失败: ' + error.message;
                    }
                }

                async function toggleAnalysis(groupId, needAnalysis) {
                    try {
                        console.log('Toggling analysis for group:', groupId, 'to:', needAnalysis);

                        const response = await fetch('/api/groups/config', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                group_id: groupId,
                                need_analysis: needAnalysis
                            })
                        });

                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }

                        const result = await response.json();
                        console.log('Toggle result:', result);

                        if (result.success) {
                            // 立即更新UI而不是重新加载整个页面
                            updateGroupStatusInUI(groupId, needAnalysis);
                        } else {
                            alert('配置更新失败: ' + result.message);
                        }

                    } catch (error) {
                        console.error('更新配置失败:', error);
                        alert('更新配置失败: ' + error.message);
                    }
                }

                function updateGroupStatusInUI(groupId, needAnalysis) {
                    // 找到对应的行
                    const rows = document.querySelectorAll('tbody tr');
                    for (const row of rows) {
                        const groupIdCell = row.querySelector('td:first-child');
                        if (groupIdCell && groupIdCell.textContent.trim() === groupId) {
                            // 更新状态显示
                            const statusCell = row.querySelector('td:nth-child(3)');
                            if (statusCell) {
                                statusCell.textContent = needAnalysis ? '已启用' : '已禁用';
                                statusCell.className = needAnalysis ? 'status-enabled' : 'status-disabled';
                            }

                            // 更新按钮
                            const button = row.querySelector('button');
                            if (button) {
                                button.textContent = needAnalysis ? '禁用分析' : '启用分析';
                                button.className = needAnalysis ? 'btn btn-danger' : 'btn btn-primary';
                                button.onclick = () => toggleAnalysis(groupId, !needAnalysis);
                            }
                            break;
                        }
                    }
                }

                async function updateGroupName(groupId, groupName) {
                    if (!groupName.trim()) return;

                    try {
                        const response = await fetch('/api/groups/config', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                group_id: groupId,
                                group_name: groupName.trim()
                            })
                        });

                        const result = await response.json();
                        if (!result.success) {
                            alert('群组名称更新失败: ' + result.message);
                        }

                    } catch (error) {
                        console.error('更新群组名称失败:', error);
                        alert('更新群组名称失败: ' + error.message);
                    }
                }

                // 页面加载时获取数据
                loadGroups();
            </script>
        </body>
        </html>
        """

    @app.get("/analysis", response_class=HTMLResponse)
    async def analysis_page():
        """分析报告页面"""
        return """
        <!DOCTYPE html>
        <html>
        <head>
            <title>分析报告 - 微信群聊分析系统</title>
            <meta charset="utf-8">
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .container { max-width: 1200px; margin: 0 auto; }
                .header { margin-bottom: 30px; }
                .back-link { color: #007bff; text-decoration: none; }
                .back-link:hover { text-decoration: underline; }
                .filters { margin: 20px 0; padding: 20px; background: #f8f9fa; border-radius: 8px; }
                .filters label { margin-right: 10px; }
                .filters input, .filters select { margin-right: 20px; padding: 5px; }
                .btn { padding: 8px 16px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
                .btn:hover { background: #0056b3; }
                .result-card { border: 1px solid #ddd; margin: 15px 0; padding: 20px; border-radius: 8px; }
                .result-header { font-weight: bold; color: #007bff; margin-bottom: 10px; }
                .loading { text-align: center; padding: 40px; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <a href="/" class="back-link">← 返回首页</a>
                    <h1>📊 分析报告</h1>
                    <p>查看群聊内容分析结果和历史数据</p>
                </div>

                <div class="filters">
                    <label>群组:</label>
                    <select id="groupFilter">
                        <option value="">所有群组</option>
                    </select>

                    <label>时间范围:</label>
                    <select id="timeFilter">
                        <option value="1">最近1小时</option>
                        <option value="6">最近6小时</option>
                        <option value="24" selected>最近24小时</option>
                        <option value="168">最近7天</option>
                    </select>

                    <button class="btn" onclick="loadResults()">刷新数据</button>
                    <button class="btn" onclick="runAnalysis()">运行新分析</button>
                </div>

                <div id="loading" class="loading" style="display: none;">正在加载分析结果...</div>
                <div id="results"></div>
            </div>

            <script>
                async function loadGroups() {
                    try {
                        const response = await fetch('/api/groups');
                        const groups = await response.json();

                        const select = document.getElementById('groupFilter');
                        groups.forEach(group => {
                            const option = document.createElement('option');
                            option.value = group.group_id;
                            option.textContent = group.group_name || group.group_id.substring(0, 30) + '...';
                            select.appendChild(option);
                        });
                    } catch (error) {
                        console.error('加载群组失败:', error);
                    }
                }

                async function loadResults() {
                    const loading = document.getElementById('loading');
                    const results = document.getElementById('results');

                    loading.style.display = 'block';
                    results.innerHTML = '';

                    try {
                        const groupId = document.getElementById('groupFilter').value;
                        const hours = document.getElementById('timeFilter').value;

                        const params = new URLSearchParams();
                        if (groupId) params.append('group_id', groupId);
                        params.append('hours', hours);
                        params.append('limit', '20');

                        const response = await fetch('/api/analysis/results?' + params);
                        const data = await response.json();

                        if (data.length === 0) {
                            results.innerHTML = '<p>暂无分析结果</p>';
                        } else {
                            data.forEach(result => {
                                const card = document.createElement('div');
                                card.className = 'result-card';
                                card.innerHTML = `
                                    <div class="result-header">
                                        群组: ${result.group_name || result.group_id.substring(0, 30) + '...'} |
                                        话题: ${result.topic_category} |
                                        时间: ${new Date(result.analysis_time).toLocaleString()}
                                    </div>
                                    <p><strong>主要话题:</strong> ${result.main_topic}</p>
                                    <p><strong>情感评分:</strong> ${result.sentiment_score}</p>
                                    <p><strong>讨论热度:</strong> ${result.discussion_heat}/10</p>
                                    <p><strong>关键信息:</strong> ${result.key_information ? result.key_information.slice(0, 2).join('; ') : '无'}</p>
                                `;
                                results.appendChild(card);
                            });
                        }

                    } catch (error) {
                        console.error('加载结果失败:', error);
                        results.innerHTML = '<p>加载失败: ' + error.message + '</p>';
                    } finally {
                        loading.style.display = 'none';
                    }
                }

                async function runAnalysis() {
                    if (!confirm('确定要运行新的分析任务吗？这可能需要几分钟时间。')) {
                        return;
                    }

                    try {
                        const response = await fetch('/api/analysis/run', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                hours: parseInt(document.getElementById('timeFilter').value),
                                sync_data: true,
                                generate_wordcloud: true
                            })
                        });

                        const result = await response.json();
                        if (result.success) {
                            alert('分析任务已启动，请稍后刷新查看结果');
                        } else {
                            alert('启动分析任务失败: ' + result.message);
                        }

                    } catch (error) {
                        console.error('运行分析失败:', error);
                        alert('运行分析失败: ' + error.message);
                    }
                }

                // 页面加载时初始化
                loadGroups();
                loadResults();
            </script>
        </body>
        </html>
        """

    @app.get("/wordcloud", response_class=HTMLResponse)
    async def wordcloud_page():
        """词云分析页面"""
        return """
        <!DOCTYPE html>
        <html>
        <head>
            <title>词云分析 - 微信群聊分析系统</title>
            <meta charset="utf-8">
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .container { max-width: 1200px; margin: 0 auto; }
                .header { margin-bottom: 30px; }
                .back-link { color: #007bff; text-decoration: none; }
                .back-link:hover { text-decoration: underline; }
                .wordcloud-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 20px; }
                .wordcloud-card { border: 1px solid #ddd; padding: 20px; border-radius: 8px; text-align: center; }
                .wordcloud-image { max-width: 100%; height: auto; border-radius: 8px; }
                .wordcloud-info { margin-top: 15px; font-size: 14px; color: #666; }
                .loading { text-align: center; padding: 40px; }
                .btn { padding: 8px 16px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 10px; }
                .btn:hover { background: #0056b3; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <a href="/" class="back-link">← 返回首页</a>
                    <h1>☁️ 词云分析</h1>
                    <p>查看群聊内容的词云可视化结果</p>
                    <button class="btn" onclick="loadWordclouds()">刷新词云</button>
                </div>

                <div id="loading" class="loading" style="display: none;">正在加载词云数据...</div>
                <div id="wordclouds" class="wordcloud-grid"></div>
            </div>

            <script>
                async function loadWordclouds() {
                    const loading = document.getElementById('loading');
                    const container = document.getElementById('wordclouds');

                    loading.style.display = 'block';
                    container.innerHTML = '';

                    try {
                        const response = await fetch('/api/wordcloud/list');
                        const wordclouds = await response.json();

                        if (wordclouds.length === 0) {
                            container.innerHTML = '<p>暂无词云数据，请先运行分析生成词云</p>';
                        } else {
                            wordclouds.forEach(wordcloud => {
                                const card = document.createElement('div');
                                card.className = 'wordcloud-card';
                                card.innerHTML = `
                                    <img src="/api/wordcloud/image/${wordcloud.filename}"
                                         alt="词云图片" class="wordcloud-image"
                                         onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjBmMGYwIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxOCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuWbvueJh+WKoOi9veWksei0pTwvdGV4dD48L3N2Zz4='">
                                    <div class="wordcloud-info">
                                        <div><strong>文件名:</strong> ${wordcloud.filename}</div>
                                        <div><strong>大小:</strong> ${(wordcloud.size / 1024).toFixed(1)} KB</div>
                                        <div><strong>创建时间:</strong> ${new Date(wordcloud.created_time).toLocaleString()}</div>
                                    </div>
                                `;
                                container.appendChild(card);
                            });
                        }

                    } catch (error) {
                        console.error('加载词云失败:', error);
                        container.innerHTML = '<p>加载失败: ' + error.message + '</p>';
                    } finally {
                        loading.style.display = 'none';
                    }
                }

                // 页面加载时初始化
                loadWordclouds();
            </script>
        </body>
        </html>
        """

    @app.get("/chat", response_class=HTMLResponse)
    async def chat_page():
        """聊天查询页面"""
        return """
        <!DOCTYPE html>
        <html>
        <head>
            <title>聊天查询 - 微信群聊分析系统</title>
            <meta charset="utf-8">
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .container { max-width: 1200px; margin: 0 auto; }
                .header { margin-bottom: 30px; }
                .back-link { color: #007bff; text-decoration: none; }
                .back-link:hover { text-decoration: underline; }
                .filters { margin: 20px 0; padding: 20px; background: #f8f9fa; border-radius: 8px; }
                .filters label { margin-right: 10px; }
                .filters input, .filters select { margin-right: 20px; padding: 5px; }
                .btn { padding: 8px 16px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
                .btn:hover { background: #0056b3; }
                .message-item { border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 8px; background: #f9f9f9; }
                .message-time { color: #666; font-size: 12px; margin-bottom: 5px; }
                .message-content { line-height: 1.5; }
                .loading { text-align: center; padding: 40px; }
                .no-results { text-align: center; padding: 40px; color: #666; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <a href="/" class="back-link">← 返回首页</a>
                    <h1>💬 聊天查询</h1>
                    <p>查看指定群组和时间范围内的聊天消息</p>
                </div>

                <div class="filters">
                    <label>群组:</label>
                    <select id="groupFilter">
                        <option value="">请选择群组</option>
                    </select>

                    <label>时间范围:</label>
                    <select id="timeFilter">
                        <option value="1">最近1小时</option>
                        <option value="6">最近6小时</option>
                        <option value="24" selected>最近24小时</option>
                        <option value="72">最近3天</option>
                        <option value="168">最近7天</option>
                    </select>

                    <label>消息数量:</label>
                    <select id="limitFilter">
                        <option value="50">50条</option>
                        <option value="100" selected>100条</option>
                        <option value="200">200条</option>
                        <option value="500">500条</option>
                    </select>

                    <button class="btn" onclick="loadMessages()">查询消息</button>
                </div>

                <div id="loading" class="loading" style="display: none;">正在加载聊天消息...</div>
                <div id="messages"></div>
            </div>

            <script>
                async function loadGroups() {
                    try {
                        const response = await fetch('/api/groups');
                        const groups = await response.json();

                        const select = document.getElementById('groupFilter');
                        groups.forEach(group => {
                            const option = document.createElement('option');
                            option.value = group.group_id;
                            option.textContent = group.group_name || group.group_id.substring(0, 30) + '...';
                            select.appendChild(option);
                        });
                    } catch (error) {
                        console.error('加载群组失败:', error);
                    }
                }

                async function loadMessages() {
                    const groupId = document.getElementById('groupFilter').value;
                    const hours = document.getElementById('timeFilter').value;
                    const limit = document.getElementById('limitFilter').value;

                    if (!groupId) {
                        alert('请选择群组');
                        return;
                    }

                    const loading = document.getElementById('loading');
                    const messages = document.getElementById('messages');

                    loading.style.display = 'block';
                    messages.innerHTML = '';

                    try {
                        const params = new URLSearchParams();
                        params.append('group_id', groupId);
                        params.append('hours', hours);
                        params.append('limit', limit);

                        const response = await fetch('/api/chat/messages?' + params);
                        const data = await response.json();

                        if (data.length === 0) {
                            messages.innerHTML = '<div class="no-results">该时间范围内没有找到聊天消息</div>';
                        } else {
                            data.forEach(message => {
                                const messageDiv = document.createElement('div');
                                messageDiv.className = 'message-item';
                                messageDiv.innerHTML = `
                                    <div class="message-time">${new Date(message.create_datetime).toLocaleString()}</div>
                                    <div class="message-content">${message.str_content}</div>
                                `;
                                messages.appendChild(messageDiv);
                            });
                        }

                    } catch (error) {
                        console.error('加载消息失败:', error);
                        messages.innerHTML = '<div class="no-results">加载失败: ' + error.message + '</div>';
                    } finally {
                        loading.style.display = 'none';
                    }
                }

                // 页面加载时初始化
                loadGroups();
            </script>
        </body>
        </html>
        """

    @app.get("/api/chat/messages")
    async def get_chat_messages(
        group_id: str = Query(...),
        hours: int = Query(24),
        limit: int = Query(100)
    ):
        """获取聊天消息"""
        try:
            messages = db_manager.get_recent_messages_for_group(
                group_id=group_id,
                hours=hours,
                limit=limit
            )

            return [message.to_dict() for message in messages]

        except Exception as e:
            logger.error(f"获取聊天消息失败: {e}")
            raise HTTPException(status_code=500, detail=str(e))

    @app.get("/api/docs", response_class=RedirectResponse)
    async def api_docs_redirect():
        """重定向到API文档"""
        return RedirectResponse(url="/docs")

    # 启动定时任务管理器
    schedule_manager.start()

    return app


async def _run_analysis_task(analysis_system: WechatAnalysisSystem, 
                           hours: int, sync_data: bool, generate_wordcloud: bool):
    """后台分析任务"""
    try:
        logger.info("开始后台分析任务")
        result = analysis_system.run_analysis(
            hours=hours,
            sync_data=sync_data,
            generate_wordcloud=generate_wordcloud
        )
        logger.info(f"后台分析任务完成: {result.get('status')}")
    except Exception as e:
        logger.error(f"后台分析任务失败: {e}")


if __name__ == "__main__":
    import uvicorn

    app = create_app()
    uvicorn.run(app, host="127.0.0.1", port=8006)
