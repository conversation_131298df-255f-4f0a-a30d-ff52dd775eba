"""
股票分析器

负责股票相关话题的深度分析，包括行情数据获取和投资建议生成。
"""

import requests
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from loguru import logger

from config import get_config


class StockAnalyzer:
    """股票分析器类"""
    
    def __init__(self):
        """初始化股票分析器"""
        self.config = get_config()
        self.deep_config = self.config.get_deep_analysis_config()
        self.stock_config = self.deep_config.get('stock', {})
        
        self.enabled = self.stock_config.get('enabled', True)
        self.data_sources = self.stock_config.get('data_sources', ['tushare', 'akshare'])
        self.score_range = self.stock_config.get('score_range', [1, 10])
    
    def analyze_stock_discussion(self, stock_mentions: List[Dict[str, Any]], 
                               key_information: List[str]) -> Dict[str, Any]:
        """
        分析股票讨论内容
        
        Args:
            stock_mentions: 股票提及信息
            key_information: 关键信息列表
            
        Returns:
            股票分析结果
        """
        if not self.enabled:
            return {'enabled': False, 'message': '股票分析功能未启用'}
        
        if not stock_mentions:
            return {'stocks': [], 'analysis': '未发现股票相关讨论'}
        
        logger.info(f"开始分析 {len(stock_mentions)} 个股票提及")
        
        analyzed_stocks = []
        
        for stock_mention in stock_mentions:
            try:
                stock_analysis = self._analyze_single_stock(stock_mention, key_information)
                analyzed_stocks.append(stock_analysis)
            except Exception as e:
                logger.warning(f"分析股票 {stock_mention.get('symbol')} 失败: {e}")
                analyzed_stocks.append({
                    'symbol': stock_mention.get('symbol', '未知'),
                    'error': str(e),
                    'analysis_status': 'failed'
                })
        
        # 生成整体分析
        overall_analysis = self._generate_overall_stock_analysis(analyzed_stocks, key_information)
        
        return {
            'stocks': analyzed_stocks,
            'overall_analysis': overall_analysis,
            'analysis_time': datetime.now().isoformat(),
            'data_sources': self.data_sources
        }
    
    def _analyze_single_stock(self, stock_mention: Dict[str, Any], 
                            key_information: List[str]) -> Dict[str, Any]:
        """分析单个股票"""
        symbol = stock_mention.get('symbol', '')
        stock_type = stock_mention.get('type', 'unknown')
        
        # 基础信息
        stock_info = {
            'symbol': symbol,
            'type': stock_type,
            'analysis_status': 'completed'
        }
        
        # 尝试获取股票数据（模拟实现，实际需要接入真实API）
        try:
            market_data = self._get_mock_market_data(symbol)
            stock_info.update(market_data)
        except Exception as e:
            logger.warning(f"获取 {symbol} 市场数据失败: {e}")
            stock_info['market_data_error'] = str(e)
        
        # 分析讨论情感
        discussion_sentiment = self._analyze_stock_discussion_sentiment(symbol, key_information)
        stock_info['discussion_sentiment'] = discussion_sentiment
        
        # 生成投资建议评分
        investment_score = self._calculate_investment_score(stock_info, key_information)
        stock_info['investment_score'] = investment_score
        
        # 生成风险评估
        risk_assessment = self._assess_stock_risk(stock_info, key_information)
        stock_info['risk_assessment'] = risk_assessment
        
        return stock_info
    
    def _get_mock_market_data(self, symbol: str) -> Dict[str, Any]:
        """
        获取模拟市场数据（实际应用中需要接入真实的股票API）
        
        Args:
            symbol: 股票代码
            
        Returns:
            市场数据
        """
        # 这里是模拟数据，实际应用中需要接入tushare、akshare等API
        import random
        
        base_price = random.uniform(10, 100)
        change_percent = random.uniform(-5, 5)
        
        return {
            'current_price': round(base_price, 2),
            'change_percent': round(change_percent, 2),
            'volume': random.randint(1000000, 10000000),
            'market_cap': round(base_price * random.randint(100000000, 1000000000), 2),
            'pe_ratio': round(random.uniform(10, 30), 2),
            'data_source': 'mock',
            'last_updated': datetime.now().isoformat()
        }
    
    def _analyze_stock_discussion_sentiment(self, symbol: str, 
                                          key_information: List[str]) -> Dict[str, Any]:
        """分析股票讨论情感"""
        # 简单的关键词情感分析
        positive_keywords = ['看好', '上涨', '买入', '推荐', '利好', '涨停','红', '红了', '抄底', 'T', '做T', '大涨']
        negative_keywords = ['看空', '下跌', '卖出', '风险', '利空', '跌停', '绿了', '绿', '割肉', '割了']
        
        positive_count = 0
        negative_count = 0
        
        for info in key_information:
            info_lower = info.lower()
            if symbol.lower() in info_lower:
                positive_count += sum(1 for kw in positive_keywords if kw in info)
                negative_count += sum(1 for kw in negative_keywords if kw in info)
        
        total_mentions = positive_count + negative_count
        
        if total_mentions == 0:
            sentiment_score = 0.0
            sentiment_label = 'neutral'
        else:
            sentiment_score = (positive_count - negative_count) / total_mentions
            if sentiment_score > 0.2:
                sentiment_label = 'positive'
            elif sentiment_score < -0.2:
                sentiment_label = 'negative'
            else:
                sentiment_label = 'neutral'
        
        return {
            'score': round(sentiment_score, 3),
            'label': sentiment_label,
            'positive_mentions': positive_count,
            'negative_mentions': negative_count,
            'total_mentions': total_mentions
        }
    
    def _calculate_investment_score(self, stock_info: Dict[str, Any], 
                                  key_information: List[str]) -> Dict[str, Any]:
        """计算投资建议评分"""
        score_factors = []
        
        # 基于市场数据的评分
        if 'current_price' in stock_info:
            change_percent = stock_info.get('change_percent', 0)
            if change_percent > 2:
                score_factors.append(('price_momentum', 2))
            elif change_percent < -2:
                score_factors.append(('price_momentum', -2))
            else:
                score_factors.append(('price_momentum', 0))
        
        # 基于讨论情感的评分
        discussion_sentiment = stock_info.get('discussion_sentiment', {})
        sentiment_score = discussion_sentiment.get('score', 0)
        score_factors.append(('discussion_sentiment', sentiment_score * 3))
        
        # 基于PE比率的评分（如果有）
        pe_ratio = stock_info.get('pe_ratio')
        if pe_ratio:
            if pe_ratio < 15:
                score_factors.append(('valuation', 1))
            elif pe_ratio > 25:
                score_factors.append(('valuation', -1))
            else:
                score_factors.append(('valuation', 0))
        
        # 计算总分
        total_score = sum(factor[1] for factor in score_factors)
        
        # 标准化到1-10分制
        min_score, max_score = self.score_range
        normalized_score = max(min_score, min(max_score, 5 + total_score))
        
        # 生成建议
        if normalized_score >= 7:
            recommendation = '建议关注'
        elif normalized_score >= 5:
            recommendation = '谨慎观望'
        else:
            recommendation = '建议回避'
        
        return {
            'score': round(normalized_score, 1),
            'recommendation': recommendation,
            'factors': score_factors,
            'score_range': self.score_range
        }
    
    def _assess_stock_risk(self, stock_info: Dict[str, Any], 
                         key_information: List[str]) -> Dict[str, Any]:
        """评估股票风险"""
        risk_factors = []
        
        # 基于价格波动的风险
        change_percent = abs(stock_info.get('change_percent', 0))
        if change_percent > 5:
            risk_factors.append('高价格波动')
        
        # 基于讨论情感的风险
        discussion_sentiment = stock_info.get('discussion_sentiment', {})
        if discussion_sentiment.get('label') == 'negative':
            risk_factors.append('负面讨论情绪')
        
        # 基于PE比率的风险
        pe_ratio = stock_info.get('pe_ratio')
        if pe_ratio and pe_ratio > 30:
            risk_factors.append('估值偏高')
        
        # 确定风险等级
        risk_count = len(risk_factors)
        if risk_count >= 3:
            risk_level = 'high'
        elif risk_count >= 1:
            risk_level = 'medium'
        else:
            risk_level = 'low'
        
        return {
            'level': risk_level,
            'factors': risk_factors,
            'assessment_time': datetime.now().isoformat()
        }
    
    def _generate_overall_stock_analysis(self, analyzed_stocks: List[Dict[str, Any]], 
                                       key_information: List[str]) -> Dict[str, Any]:
        """生成整体股票分析"""
        if not analyzed_stocks:
            return {'summary': '无股票分析数据'}
        
        # 统计成功分析的股票
        successful_analyses = [stock for stock in analyzed_stocks 
                             if stock.get('analysis_status') == 'completed']
        
        if not successful_analyses:
            return {'summary': '所有股票分析均失败'}
        
        # 计算平均投资评分
        scores = [stock.get('investment_score', {}).get('score', 5) 
                 for stock in successful_analyses]
        avg_score = sum(scores) / len(scores) if scores else 5
        
        # 统计风险分布
        risk_distribution = {}
        for stock in successful_analyses:
            risk_level = stock.get('risk_assessment', {}).get('level', 'medium')
            risk_distribution[risk_level] = risk_distribution.get(risk_level, 0) + 1
        
        # 找出最受关注的股票
        most_discussed = max(successful_analyses, 
                           key=lambda x: x.get('discussion_sentiment', {}).get('total_mentions', 0))
        
        return {
            'total_stocks': len(analyzed_stocks),
            'successful_analyses': len(successful_analyses),
            'average_investment_score': round(avg_score, 1),
            'risk_distribution': risk_distribution,
            'most_discussed_stock': most_discussed.get('symbol', '未知'),
            'summary': f'分析了{len(successful_analyses)}只股票，平均投资评分{avg_score:.1f}分'
        }
