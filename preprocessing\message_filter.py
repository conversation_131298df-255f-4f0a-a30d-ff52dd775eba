"""
消息过滤器

负责消息的过滤、清洗和验证。
"""

import re
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from loguru import logger

from database.models import MessageData
from config import get_config


class MessageFilter:
    """消息过滤器类"""
    
    def __init__(self):
        """初始化消息过滤器"""
        self.config = get_config()
        self.analysis_config = self.config.get_analysis_config()
        
        # 获取配置参数
        self.min_length = self.analysis_config.get('min_message_length', 2)
        self.max_length = self.analysis_config.get('max_message_length', 1000)
        self.allowed_types = self.analysis_config.get('message_types', [1])
        
        # 编译正则表达式
        self._compile_patterns()
    
    def _compile_patterns(self):
        """编译常用的正则表达式模式"""
        # 系统消息模式
        self.system_patterns = [
            re.compile(r'^".*"撤回了一条消息$'),
            re.compile(r'^系统消息：'),
            re.compile(r'^您已添加了'),
            re.compile(r'^.*邀请.*加入了群聊$'),
            re.compile(r'^.*修改群名为'),
            re.compile(r'^以上是打招呼的内容$'),
        ]
        
        # 无意义内容模式
        self.meaningless_patterns = [
            re.compile(r'^[。，、；：！？""''（）【】\s]*$'),  # 只有标点符号和空白
            re.compile(r'^[哈嗯嗯嗯啊哦呃额]{2,}$'),  # 重复的语气词
            re.compile(r'^[+\-=_]{3,}$'),  # 重复的符号
            re.compile(r'^\[.*\]$'),  # 表情符号
        ]
        
        # 垃圾信息模式
        self.spam_patterns = [
            re.compile(r'(加微信|加QQ|联系方式)', re.IGNORECASE),
            re.compile(r'(广告|推广|营销)', re.IGNORECASE),
            re.compile(r'(点击链接|立即下载)', re.IGNORECASE),
        ]
    
    def filter_messages(self, messages: List[MessageData]) -> List[MessageData]:
        """
        过滤消息列表
        
        Args:
            messages: 原始消息列表
            
        Returns:
            过滤后的消息列表
        """
        if not messages:
            return []
        
        filtered_messages = []
        stats = {
            'total': len(messages),
            'type_filtered': 0,
            'length_filtered': 0,
            'system_filtered': 0,
            'meaningless_filtered': 0,
            'spam_filtered': 0,
            'duplicate_filtered': 0,
            'valid': 0
        }
        
        seen_contents = set()  # 用于去重
        
        for message in messages:
            # 检查消息类型
            if not self._is_valid_type(message):
                stats['type_filtered'] += 1
                continue
            
            # 检查消息长度
            if not self._is_valid_length(message):
                stats['length_filtered'] += 1
                continue
            
            # 检查是否为系统消息
            if self._is_system_message(message):
                stats['system_filtered'] += 1
                continue
            
            # 检查是否为无意义内容
            if self._is_meaningless_content(message):
                stats['meaningless_filtered'] += 1
                continue
            
            # 检查是否为垃圾信息
            if self._is_spam_content(message):
                stats['spam_filtered'] += 1
                continue
            
            # 去重检查
            content_key = f"{message.str_talker}:{message.str_content.strip()}"
            if content_key in seen_contents:
                stats['duplicate_filtered'] += 1
                continue
            
            seen_contents.add(content_key)
            filtered_messages.append(message)
            stats['valid'] += 1
        
        logger.info(f"消息过滤统计: {stats}")
        return filtered_messages
    
    def _is_valid_type(self, message: MessageData) -> bool:
        """检查消息类型是否有效"""
        return message.type in self.allowed_types
    
    def _is_valid_length(self, message: MessageData) -> bool:
        """检查消息长度是否有效"""
        content_length = len(message.str_content.strip())
        return self.min_length <= content_length <= self.max_length
    
    def _is_system_message(self, message: MessageData) -> bool:
        """检查是否为系统消息"""
        content = message.str_content.strip()
        return any(pattern.match(content) for pattern in self.system_patterns)
    
    def _is_meaningless_content(self, message: MessageData) -> bool:
        """检查是否为无意义内容"""
        content = message.str_content.strip()
        return any(pattern.match(content) for pattern in self.meaningless_patterns)
    
    def _is_spam_content(self, message: MessageData) -> bool:
        """检查是否为垃圾信息"""
        content = message.str_content.strip()
        return any(pattern.search(content) for pattern in self.spam_patterns)
    
    def clean_message_content(self, content: str) -> str:
        """
        清洗消息内容
        
        Args:
            content: 原始消息内容
            
        Returns:
            清洗后的消息内容
        """
        if not content:
            return ""
        
        # 去除首尾空白
        cleaned = content.strip()
        
        # 替换多个连续空白为单个空格
        cleaned = re.sub(r'\s+', ' ', cleaned)
        
        # 去除特殊控制字符
        cleaned = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f]', '', cleaned)
        
        # 处理常见的转义字符
        cleaned = cleaned.replace('&lt;', '<').replace('&gt;', '>').replace('&amp;', '&')
        
        return cleaned
    
    def group_messages_by_time(self, messages: List[MessageData], 
                             window_minutes: int = 5) -> List[List[MessageData]]:
        """
        按时间窗口分组消息
        
        Args:
            messages: 消息列表
            window_minutes: 时间窗口大小（分钟）
            
        Returns:
            分组后的消息列表
        """
        if not messages:
            return []
        
        # 按时间排序
        sorted_messages = sorted(messages, key=lambda m: m.create_datetime)
        
        groups = []
        current_group = [sorted_messages[0]]
        window_delta = timedelta(minutes=window_minutes)
        
        for message in sorted_messages[1:]:
            # 检查是否在当前时间窗口内
            time_diff = message.create_datetime - current_group[0].create_datetime
            
            if time_diff <= window_delta:
                current_group.append(message)
            else:
                # 开始新的分组
                groups.append(current_group)
                current_group = [message]
        
        # 添加最后一个分组
        if current_group:
            groups.append(current_group)
        
        logger.debug(f"将 {len(messages)} 条消息分为 {len(groups)} 个时间窗口")
        return groups
    
    def extract_keywords(self, content: str) -> List[str]:
        """
        从消息内容中提取关键词
        
        Args:
            content: 消息内容
            
        Returns:
            关键词列表
        """
        if not content:
            return []
        
        # 简单的关键词提取（可以后续优化为使用NLP库）
        keywords = []
        
        # 提取股票代码
        stock_codes = re.findall(r'[0-9]{6}|[A-Z]{2,4}', content)
        keywords.extend(stock_codes)
        
        # 提取@用户
        mentions = re.findall(r'@[\w\u4e00-\u9fff]+', content)
        keywords.extend(mentions)
        
        # 提取话题标签
        hashtags = re.findall(r'#[\w\u4e00-\u9fff]+#?', content)
        keywords.extend(hashtags)
        
        # 提取URL
        urls = re.findall(r'https?://[^\s]+', content)
        keywords.extend(urls)
        
        return list(set(keywords))  # 去重
    
    def detect_message_intent(self, content: str) -> str:
        """
        检测消息意图
        
        Args:
            content: 消息内容
            
        Returns:
            消息意图类型
        """
        content_lower = content.lower()
        
        # 问题类型
        if any(word in content_lower for word in ['？', '?', '怎么', '如何', '为什么', '什么']):
            return 'question'
        
        # 分享类型
        if any(word in content_lower for word in ['分享', '推荐', '看看', '链接', 'http']):
            return 'share'
        
        # 讨论类型
        if any(word in content_lower for word in ['觉得', '认为', '我觉得', '个人认为']):
            return 'discussion'
        
        # 新闻类型
        if any(word in content_lower for word in ['新闻', '消息', '报道', '据说']):
            return 'news'
        
        # 默认为普通聊天
        return 'chat'
