# 微信群聊内容智能分析系统

## 项目简介

基于大模型的微信群聊内容智能分析系统，能够自动分析群聊内容、识别讨论话题、进行深度分析并生成报告。

## 主要功能

- **数据预处理**：从微信数据库获取最近1小时的消息数据
- **智能分析**：使用豆包API进行话题分类、情感分析、关键信息提取
- **深度分析**：
  - 股票话题：获取行情数据和新闻资讯，生成投资建议评分
  - 时事话题：搜索相关资讯，分析对金融市场的影响
  - 链接内容：自动解析链接内容并生成摘要
  - 其他话题：进行知识扩展和相关资料检索
- **结果通知**：通过企业微信机器人发送分析报告

## 技术架构

```
微信群聊内容智能分析系统/
├── config/                 # 配置管理
├── database/              # 数据库操作
├── preprocessing/         # 数据预处理
├── analysis/             # 智能分析
├── deep_analysis/        # 深度分析
├── notification/         # 消息通知
├── tests/               # 测试用例
├── docs/                # 文档
├── logs/                # 日志文件
└── main.py              # 主程序入口
```

## 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone <repository_url>
cd WxAnalysis

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置设置

```bash
# 复制环境变量模板
cp .env.template .env

# 编辑 .env 文件，填入实际的API密钥等配置
```

### 3. 运行系统

```bash
# 运行主程序
python main.py

# 或运行定时任务
python main.py --schedule
```

## 配置说明

主要配置文件：
- `config/config.yaml`：系统主配置文件
- `.env`：环境变量配置文件

关键配置项：
- `ARK_API_KEY`：豆包API密钥
- `WECHAT_WEBHOOK_KEY`：企业微信机器人密钥
- `WECHAT_DB_PATH`：微信数据库路径

## 开发指南

### 代码规范
- 使用中文注释和文档字符串
- 遵循PEP8代码规范
- 实现完整的异常处理机制
- 添加详细的日志记录

### 测试
```bash
# 运行单元测试
pytest tests/

# 运行测试并生成覆盖率报告
pytest --cov=. tests/
```

## 许可证

MIT License

## 贡献指南

欢迎提交Issue和Pull Request来改进项目。

## 联系方式

如有问题，请通过Issue联系。
