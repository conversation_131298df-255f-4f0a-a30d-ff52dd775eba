"""
新闻分析器

负责时事新闻话题的深度分析，包括新闻搜索、影响评估等功能。
"""

import re
import requests
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from loguru import logger

from config import get_config


class NewsAnalyzer:
    """新闻分析器类"""

    def __init__(self):
        """初始化新闻分析器"""
        self.config = get_config()
        self.deep_config = self.config.get_deep_analysis_config()
        self.news_config = self.deep_config.get('news', {})

        self.enabled = self.news_config.get('enabled', True)
        self.search_engines = self.news_config.get('search_engines', ['baidu', 'bing'])
        self.max_search_results = self.news_config.get('max_search_results', 10)

        # 新闻关键词模式
        self.news_patterns = [
            re.compile(r'(最新|今日|近期|刚刚|突发|重磅).*?(新闻|消息|报道)', re.IGNORECASE),
            re.compile(r'(据.*?报道|消息称|有消息|传闻)', re.IGNORECASE),
            re.compile(r'(发布|宣布|公告|声明).*?(重要|重大)', re.IGNORECASE),
        ]

        # 影响评估关键词
        self.impact_keywords = {
            'positive': ['利好', '上涨', '增长', '突破', '创新', '合作', '投资', '发展'],
            'negative': ['下跌', '风险', '危机', '问题', '困难', '损失', '减少', '停止'],
            'neutral': ['维持', '稳定', '持平', '观望', '等待', '关注']
        }

    def analyze_news_discussion(self, news_events: List[Dict[str, Any]],
                              key_information: List[str]) -> Dict[str, Any]:
        """
        分析新闻讨论内容

        Args:
            news_events: 新闻事件信息
            key_information: 关键信息列表

        Returns:
            新闻分析结果
        """
        if not self.enabled:
            return {'enabled': False, 'message': '新闻分析功能未启用'}

        logger.info(f"开始分析 {len(news_events)} 个新闻事件")

        analyzed_events = []

        for event in news_events:
            try:
                event_analysis = self._analyze_single_event(event, key_information)
                analyzed_events.append(event_analysis)
            except Exception as e:
                logger.warning(f"分析新闻事件失败: {e}")
                analyzed_events.append({
                    'event': event.get('event', '未知事件'),
                    'error': str(e),
                    'analysis_status': 'failed'
                })

        # 生成整体分析
        overall_analysis = self._generate_overall_news_analysis(analyzed_events, key_information)

        return {
            'events': analyzed_events,
            'overall_analysis': overall_analysis,
            'analysis_time': datetime.now().isoformat(),
            'search_engines': self.search_engines
        }

    def _analyze_single_event(self, event: Dict[str, Any],
                            key_information: List[str]) -> Dict[str, Any]:
        """分析单个新闻事件"""
        event_name = event.get('event', '')
        event_type = event.get('type', 'unknown')

        # 基础信息
        event_info = {
            'event': event_name,
            'type': event_type,
            'analysis_status': 'completed'
        }

        # 搜索相关新闻
        try:
            search_results = self._search_related_news(event_name)
            event_info['search_results'] = search_results
        except Exception as e:
            logger.warning(f"搜索新闻失败 {event_name}: {e}")
            event_info['search_error'] = str(e)

        # 分析市场影响
        market_impact = self._analyze_market_impact(event_name, key_information)
        event_info['market_impact'] = market_impact

        # 生成影响评分
        impact_score = self._calculate_impact_score(event_info, key_information)
        event_info['impact_score'] = impact_score

        # 生成关注建议
        attention_level = self._assess_attention_level(event_info, key_information)
        event_info['attention_level'] = attention_level

        return event_info

    def _search_related_news(self, query: str) -> List[Dict[str, Any]]:
        """
        搜索相关新闻（模拟实现）

        Args:
            query: 搜索关键词

        Returns:
            新闻搜索结果列表
        """
        # 这里是模拟实现，实际应用中需要接入真实的新闻API
        # 如百度新闻API、必应新闻API等

        mock_results = []

        # 模拟搜索结果
        for i in range(min(3, self.max_search_results)):
            mock_result = {
                'title': f'关于"{query}"的相关新闻 {i+1}',
                'url': f'https://example.com/news/{i+1}',
                'source': f'新闻源{i+1}',
                'publish_time': (datetime.now() - timedelta(hours=i*2)).isoformat(),
                'summary': f'这是关于{query}的新闻摘要内容...',
                'relevance_score': 0.9 - i * 0.1
            }
            mock_results.append(mock_result)

        logger.debug(f"模拟搜索到 {len(mock_results)} 条相关新闻")
        return mock_results

    def _analyze_market_impact(self, event_name: str,
                             key_information: List[str]) -> Dict[str, Any]:
        """分析市场影响"""
        impact_analysis = {
            'direction': 'neutral',
            'intensity': 'low',
            'sectors': [],
            'reasoning': []
        }

        # 分析影响方向
        positive_count = 0
        negative_count = 0

        # 检查事件名称中的关键词
        event_lower = event_name.lower()
        for keyword in self.impact_keywords['positive']:
            if keyword in event_lower:
                positive_count += 1

        for keyword in self.impact_keywords['negative']:
            if keyword in event_lower:
                negative_count += 1

        # 检查关键信息中的相关内容
        for info in key_information:
            info_lower = info.lower()
            if event_name.lower() in info_lower:
                for keyword in self.impact_keywords['positive']:
                    if keyword in info_lower:
                        positive_count += 1

                for keyword in self.impact_keywords['negative']:
                    if keyword in info_lower:
                        negative_count += 1

        # 确定影响方向
        if positive_count > negative_count:
            impact_analysis['direction'] = 'positive'
            impact_analysis['reasoning'].append(f'发现{positive_count}个积极因素')
        elif negative_count > positive_count:
            impact_analysis['direction'] = 'negative'
            impact_analysis['reasoning'].append(f'发现{negative_count}个消极因素')
        else:
            impact_analysis['direction'] = 'neutral'
            impact_analysis['reasoning'].append('积极和消极因素相当')

        # 确定影响强度
        total_impact = positive_count + negative_count
        if total_impact >= 3:
            impact_analysis['intensity'] = 'high'
        elif total_impact >= 1:
            impact_analysis['intensity'] = 'medium'
        else:
            impact_analysis['intensity'] = 'low'

        # 识别可能受影响的行业
        sector_keywords = {
            '科技': ['科技', '技术', '互联网', '软件', '硬件', 'AI', '人工智能'],
            '金融': ['银行', '保险', '证券', '基金', '金融', '货币'],
            '医药': ['医药', '医疗', '健康', '药品', '疫苗', '治疗'],
            '能源': ['石油', '天然气', '煤炭', '电力', '新能源', '太阳能'],
            '房地产': ['房地产', '地产', '房价', '楼市', '住房'],
            '消费': ['消费', '零售', '电商', '购物', '品牌']
        }

        for sector, keywords in sector_keywords.items():
            if any(keyword in event_lower for keyword in keywords):
                impact_analysis['sectors'].append(sector)

        return impact_analysis

    def _calculate_impact_score(self, event_info: Dict[str, Any],
                              key_information: List[str]) -> Dict[str, Any]:
        """计算影响评分"""
        score_factors = []

        # 基于市场影响的评分
        market_impact = event_info.get('market_impact', {})
        direction = market_impact.get('direction', 'neutral')
        intensity = market_impact.get('intensity', 'low')

        if direction == 'positive':
            if intensity == 'high':
                score_factors.append(('positive_impact', 3))
            elif intensity == 'medium':
                score_factors.append(('positive_impact', 2))
            else:
                score_factors.append(('positive_impact', 1))
        elif direction == 'negative':
            if intensity == 'high':
                score_factors.append(('negative_impact', -3))
            elif intensity == 'medium':
                score_factors.append(('negative_impact', -2))
            else:
                score_factors.append(('negative_impact', -1))

        # 基于搜索结果的评分
        search_results = event_info.get('search_results', [])
        if search_results:
            avg_relevance = sum(r.get('relevance_score', 0) for r in search_results) / len(search_results)
            score_factors.append(('news_relevance', avg_relevance * 2))

        # 基于涉及行业数量的评分
        sectors = market_impact.get('sectors', [])
        if len(sectors) > 2:
            score_factors.append(('sector_coverage', 1))

        # 计算总分
        total_score = sum(factor[1] for factor in score_factors)

        # 标准化到1-10分制
        normalized_score = max(1, min(10, 5 + total_score))

        # 生成建议
        if normalized_score >= 7:
            recommendation = '高度关注'
        elif normalized_score >= 5:
            recommendation = '适度关注'
        else:
            recommendation = '一般关注'

        return {
            'score': round(normalized_score, 1),
            'recommendation': recommendation,
            'factors': score_factors,
            'score_range': [1, 10]
        }

    def _assess_attention_level(self, event_info: Dict[str, Any],
                              key_information: List[str]) -> Dict[str, Any]:
        """评估关注级别"""
        attention_factors = []

        # 基于影响评分
        impact_score = event_info.get('impact_score', {}).get('score', 5)
        if impact_score >= 8:
            attention_factors.append('高影响评分')

        # 基于涉及行业
        sectors = event_info.get('market_impact', {}).get('sectors', [])
        if len(sectors) >= 3:
            attention_factors.append('多行业影响')

        # 基于新闻数量
        search_results = event_info.get('search_results', [])
        if len(search_results) >= 5:
            attention_factors.append('高媒体关注度')

        # 确定关注级别
        factor_count = len(attention_factors)
        if factor_count >= 2:
            level = 'high'
        elif factor_count >= 1:
            level = 'medium'
        else:
            level = 'low'

        return {
            'level': level,
            'factors': attention_factors,
            'assessment_time': datetime.now().isoformat()
        }

    def _generate_overall_news_analysis(self, analyzed_events: List[Dict[str, Any]],
                                      key_information: List[str]) -> Dict[str, Any]:
        """生成整体新闻分析"""
        if not analyzed_events:
            return {'summary': '无新闻事件分析数据'}

        # 统计成功分析的事件
        successful_analyses = [event for event in analyzed_events
                             if event.get('analysis_status') == 'completed']

        if not successful_analyses:
            return {'summary': '所有新闻事件分析均失败'}

        # 计算平均影响评分
        scores = [event.get('impact_score', {}).get('score', 5)
                 for event in successful_analyses]
        avg_score = sum(scores) / len(scores) if scores else 5

        # 统计关注级别分布
        attention_distribution = {}
        for event in successful_analyses:
            level = event.get('attention_level', {}).get('level', 'low')
            attention_distribution[level] = attention_distribution.get(level, 0) + 1

        # 统计影响方向
        impact_directions = {}
        for event in successful_analyses:
            direction = event.get('market_impact', {}).get('direction', 'neutral')
            impact_directions[direction] = impact_directions.get(direction, 0) + 1

        # 找出最重要的事件
        most_important = max(successful_analyses,
                           key=lambda x: x.get('impact_score', {}).get('score', 0))

        return {
            'total_events': len(analyzed_events),
            'successful_analyses': len(successful_analyses),
            'average_impact_score': round(avg_score, 1),
            'attention_distribution': attention_distribution,
            'impact_directions': impact_directions,
            'most_important_event': most_important.get('event', '未知'),
            'summary': f'分析了{len(successful_analyses)}个新闻事件，平均影响评分{avg_score:.1f}分'
        }
