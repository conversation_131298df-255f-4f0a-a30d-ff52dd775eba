# 微信群聊内容智能分析系统需求分析文档

## 1. 项目概述

### 1.1 项目背景
随着微信群聊在日常沟通中的广泛应用，群聊中产生了大量有价值的信息，包括股票讨论、时事新闻、链接分享等。为了更好地理解和利用这些信息，需要开发一个智能分析系统，能够自动分析群聊内容并生成有价值的洞察。

### 1.2 项目目标
- 自动分析微信群聊内容，识别讨论话题
- 对不同类型话题进行深度分析
- 生成智能化的分析报告
- 通过企业微信机器人及时推送分析结果

### 1.3 项目范围
- 支持从微信数据库读取聊天记录
- 支持文本消息的智能分析
- 支持股票、时事、链接等多种话题类型
- 支持情感分析和热度评估
- 支持分析结果的持久化存储
- 支持企业微信通知推送

## 2. 功能需求

### 2.1 核心功能

#### 2.1.1 数据获取与预处理
**功能描述**：从微信数据库获取最近1小时的群聊消息并进行预处理

**详细需求**：
- 从SQLite数据库MSG表读取消息数据
- 按StrTalker字段区分不同群组
- 只处理Type=1的文本消息
- 过滤系统消息、垃圾信息、重复消息
- 清洗消息内容，去除特殊字符

**输入**：微信数据库文件路径、时间窗口参数
**输出**：按群组分类的清洗后消息列表

#### 2.1.2 智能内容分析
**功能描述**：使用豆包大模型API对群聊内容进行智能分析

**详细需求**：
- 话题识别与分类（股票、时事、链接、哲学感悟、其他）
- 情感倾向分析（积极、消极、中性）
- 关键信息提取
- 讨论热度评估（1-10分制）
- 实体识别（股票代码、公司名称、人名、地点等）

**输入**：格式化的群聊消息文本
**输出**：结构化的分析结果JSON

#### 2.1.3 深度分析处理
**功能描述**：针对不同话题类型进行专门的深度分析

**股票话题分析**：
- 识别股票代码和公司名称
- 获取实时行情数据（可扩展接入tushare、akshare）
- 搜索相关新闻资讯
- 生成投资建议评分（1-10分制）
- 评估投资风险等级

**时事话题分析**：
- 搜索相关新闻和资讯
- 分析对金融市场的潜在影响
- 生成影响评估报告

**链接内容分析**：
- 自动访问并解析链接内容
- 生成内容摘要
- 分类链接类型（新闻、视频、技术等）

**其他话题分析**：
- 哲学感悟类内容的知识扩展
- 相关资料检索和整理

#### 2.1.4 结果存储与管理
**功能描述**：将分析结果持久化存储并提供查询功能

**详细需求**：
- 设计分析结果数据库表结构
- 存储分析时间、群组信息、话题分类、分析结果等
- 支持按时间、群组、话题类型查询
- 提供分析统计功能

#### 2.1.5 消息通知推送
**功能描述**：通过企业微信机器人推送分析报告

**详细需求**：
- 格式化分析结果为易读的报告
- 支持不同格式的报告（文本、Markdown、JSON）
- 通过企业微信Webhook发送通知
- 支持错误通知和系统状态通知

### 2.2 非功能需求

#### 2.2.1 性能需求
- 单次分析处理时间不超过5分钟
- 支持处理1000条消息以内的群聊数据
- 数据库查询响应时间不超过3秒

#### 2.2.2 可靠性需求
- 系统可用性达到99%
- 具备完善的错误处理机制
- 支持API调用失败重试
- 提供详细的日志记录

#### 2.2.3 安全性需求
- API密钥等敏感信息通过环境变量管理
- 数据库连接使用安全配置
- 不在日志中记录敏感信息

#### 2.2.4 可维护性需求
- 代码结构清晰，模块化设计
- 提供完整的中文注释和文档
- 支持配置文件管理
- 提供单元测试和集成测试

## 3. 技术需求

### 3.1 开发环境
- Python 3.8+
- SQLite数据库
- 豆包大模型API
- 企业微信机器人API

### 3.2 主要依赖
- openai：豆包API调用
- requests：HTTP请求
- pandas：数据处理
- beautifulsoup4：网页内容解析
- loguru：日志管理
- pyyaml：配置文件解析

### 3.3 架构要求
- 采用模块化设计
- 支持配置文件管理
- 实现完整的异常处理
- 提供灵活的扩展接口

## 4. 约束条件

### 4.1 技术约束
- 必须使用提供的豆包API进行内容分析
- 必须从指定的微信数据库读取数据
- 必须通过指定的企业微信机器人发送通知

### 4.2 业务约束
- 只分析最近1小时内的消息
- 只处理文本类型消息
- 分析结果必须包含中文说明

### 4.3 时间约束
- 项目开发周期：按需完成
- 单次分析执行时间：不超过5分钟

## 5. 验收标准

### 5.1 功能验收
- [ ] 能够成功连接微信数据库并读取消息
- [ ] 能够调用豆包API进行内容分析
- [ ] 能够识别和分类不同类型话题
- [ ] 能够进行情感分析和热度评估
- [ ] 能够进行股票等专门话题的深度分析
- [ ] 能够存储分析结果到数据库
- [ ] 能够通过企业微信发送分析报告

### 5.2 质量验收
- [ ] 代码覆盖率达到80%以上
- [ ] 通过所有单元测试和集成测试
- [ ] 提供完整的中文文档
- [ ] 具备完善的错误处理机制

### 5.3 性能验收
- [ ] 单次分析时间不超过5分钟
- [ ] 系统能稳定运行24小时以上
- [ ] API调用成功率达到95%以上

## 6. 风险分析

### 6.1 技术风险
- **豆包API限制**：API调用频率限制或服务不稳定
- **数据库访问**：微信数据库结构变化或访问权限问题
- **网络依赖**：外部API和网络服务的可用性

### 6.2 业务风险
- **数据质量**：群聊消息质量参差不齐，影响分析效果
- **隐私合规**：群聊内容分析的隐私和合规问题

### 6.3 风险应对
- 实现API调用重试机制
- 提供数据库连接异常处理
- 设计降级方案，在外部服务不可用时提供基础功能
- 确保只分析必要的信息，保护用户隐私
