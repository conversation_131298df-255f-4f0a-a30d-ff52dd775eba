#!/usr/bin/env python3
"""
测试定时任务是否会发送重复的分析报告
"""

import sys
import os
import time
from datetime import datetime
from unittest.mock import patch, MagicMock

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_duplicate_reports():
    """测试是否会发送重复的分析报告"""
    print("🧪 测试定时任务重复发送分析报告...")
    
    # 记录发送次数
    send_count = 0
    original_send_method = None
    
    def mock_send_analysis_report(*args, **kwargs):
        nonlocal send_count
        send_count += 1
        print(f"   📧 第{send_count}次调用send_analysis_report")
        print(f"      参数: args={len(args)}, kwargs={kwargs}")
        return True
    
    try:
        from web.task_manager import AsyncTaskManager
        from notification.wechat_notifier import WechatNotifier
        
        # 保存原始方法
        original_send_method = WechatNotifier.send_analysis_report
        
        # 替换为模拟方法
        WechatNotifier.send_analysis_report = mock_send_analysis_report
        
        print("   创建分析任务...")
        task_manager = AsyncTaskManager()
        
        # 创建一个启用发送分析报告的任务
        task_id = task_manager.create_analysis_task(
            hours=1,
            sync_data=False,
            generate_wordcloud=False,
            send_wordcloud_notification=False,
            send_analysis_report=True  # 启用发送分析报告
        )
        
        print(f"   任务ID: {task_id}")
        
        # 等待任务完成
        print("   等待任务完成...")
        for i in range(60):  # 等待最多60秒
            task_info = task_manager.get_task_info(task_id)
            if task_info.status.name in ['COMPLETED', 'FAILED']:
                print(f"   任务状态: {task_info.status.name}")
                break
            time.sleep(1)
        else:
            print("   ⚠️ 任务超时")
        
        # 检查发送次数
        print(f"\n📊 测试结果:")
        print(f"   send_analysis_report调用次数: {send_count}")
        
        if send_count == 0:
            print("   ✅ 没有发送分析报告（可能是因为没有数据或其他原因）")
            return True
        elif send_count == 1:
            print("   ✅ 正常：只发送了一次分析报告")
            return True
        else:
            print(f"   ❌ 异常：发送了{send_count}次分析报告，存在重复发送问题！")
            return False
            
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 恢复原始方法
        if original_send_method:
            WechatNotifier.send_analysis_report = original_send_method

def test_schedule_task_logic():
    """测试定时任务逻辑"""
    print("\n🧪 测试定时任务逻辑...")
    
    try:
        from web.schedule_manager import ScheduleManager
        
        schedule_manager = ScheduleManager()
        config = schedule_manager.load_config()
        
        print(f"   定时任务配置:")
        print(f"     enabled: {config.get('enabled')}")
        print(f"     send_analysis_report: {config.get('send_analysis_report')}")
        print(f"     interval_minutes: {config.get('interval_minutes')}")
        
        if config.get('enabled') and config.get('send_analysis_report'):
            print("   ⚠️ 定时任务已启用且配置为发送分析报告")
            print("   这可能是重复发送的原因之一")
        else:
            print("   ✅ 定时任务未启用或未配置发送分析报告")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        return False

def test_main_run_analysis():
    """测试main.py中的run_analysis方法"""
    print("\n🧪 测试main.py的run_analysis方法...")
    
    # 记录发送次数
    send_count = 0
    original_send_method = None
    
    def mock_send_analysis_report(*args, **kwargs):
        nonlocal send_count
        send_count += 1
        print(f"   📧 main.py第{send_count}次调用send_analysis_report")
        return True
    
    try:
        from main import WechatAnalysisSystem
        from notification.wechat_notifier import WechatNotifier
        
        # 保存原始方法
        original_send_method = WechatNotifier.send_analysis_report
        
        # 替换为模拟方法
        WechatNotifier.send_analysis_report = mock_send_analysis_report
        
        print("   创建分析系统...")
        system = WechatAnalysisSystem()
        
        print("   调用run_analysis方法...")
        result = system.run_analysis(
            hours=1,
            sync_data=False,
            generate_wordcloud=False,
            send_wordcloud_notification=False
        )
        
        print(f"   分析结果状态: {result.get('status')}")
        print(f"   send_analysis_report调用次数: {send_count}")
        
        if send_count == 1:
            print("   ⚠️ main.py的run_analysis方法会自动发送分析报告")
            print("   这可能导致与任务管理器的发送逻辑重复")
            return False
        else:
            print("   ✅ main.py的run_analysis方法没有发送分析报告")
            return True
            
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 恢复原始方法
        if original_send_method:
            WechatNotifier.send_analysis_report = original_send_method

def main():
    """主测试函数"""
    print("🚀 开始测试定时任务重复发送问题...")
    print("=" * 60)
    
    tests = [
        ("任务管理器重复发送测试", test_duplicate_reports),
        ("定时任务逻辑测试", test_schedule_task_logic),
        ("main.py发送逻辑测试", test_main_run_analysis)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            print(f"\n📋 测试: {test_name}")
            if test_func():
                passed += 1
                print(f"   ✅ {test_name} - 通过")
            else:
                print(f"   ❌ {test_name} - 失败")
        except Exception as e:
            print(f"   ❌ {test_name} - 执行失败: {e}")
    
    print("\n" + "=" * 60)
    print(f"🎯 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 没有发现重复发送问题！")
    else:
        print("⚠️ 发现了重复发送问题，需要修复")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
