#!/usr/bin/env python3
"""
测试修复结果的脚本

验证所有修复是否正常工作：
1. datetime错误修复
2. 新增的API接口
3. 定时任务功能
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_datetime_fix():
    """测试datetime错误修复"""
    print("🧪 测试datetime错误修复...")
    try:
        from web.task_manager import task_manager

        # 尝试创建一个分析任务
        task_id = task_manager.create_analysis_task(
            hours=1,
            sync_data=True,
            generate_wordcloud=True,
            send_wordcloud_notification=False,
            send_analysis_report=True
        )

        print(f"✅ datetime错误已修复，成功创建任务: {task_id}")
        return True

    except Exception as e:
        print(f"❌ datetime错误仍存在: {e}")
        return False

def test_schedule_manager():
    """测试定时任务管理器"""
    print("\n🧪 测试定时任务管理器...")
    try:
        from web.schedule_manager import ScheduleManager
        
        schedule_manager = ScheduleManager()
        
        # 测试配置加载
        config = schedule_manager.load_config()
        print(f"✅ 配置加载成功: {config}")
        
        # 测试静默时间段检查
        test_config = {
            "silent_start": "23:00",
            "silent_end": "07:00"
        }
        is_silent = schedule_manager.is_in_silent_period(test_config)
        print(f"✅ 静默时间段检查: {is_silent}")
        
        # 测试配置保存
        test_save_config = {
            "interval_minutes": 120,
            "send_analysis_report": True,
            "send_wordcloud": False,
            "silent_start": "22:00",
            "silent_end": "08:00",
            "enabled": True
        }
        
        success = schedule_manager.save_config(test_save_config)
        print(f"✅ 配置保存成功: {success}")
        
        return True
        
    except Exception as e:
        print(f"❌ 定时任务管理器测试失败: {e}")
        return False

def test_api_models():
    """测试API数据模型"""
    print("\n🧪 测试API数据模型...")
    try:
        from web.api_server import AnalysisRequest, ScheduleConfigRequest
        
        # 测试AnalysisRequest
        analysis_req = AnalysisRequest(
            hours=2,
            sync_data=True,
            generate_wordcloud=True,
            send_wordcloud_notification=False,
            send_analysis_report=True
        )
        print(f"✅ AnalysisRequest创建成功: {analysis_req}")
        
        # 测试ScheduleConfigRequest
        schedule_req = ScheduleConfigRequest(
            interval_minutes=60,
            send_analysis_report=True,
            send_wordcloud=True,
            silent_start="23:00",
            silent_end="07:00",
            enabled=True
        )
        print(f"✅ ScheduleConfigRequest创建成功: {schedule_req}")
        
        return True
        
    except Exception as e:
        print(f"❌ API数据模型测试失败: {e}")
        return False

def test_database_query():
    """测试数据库查询修复"""
    print("\n🧪 测试数据库查询修复...")
    try:
        from database.db_manager import DatabaseManager
        
        db_manager = DatabaseManager()
        results = db_manager.get_analysis_results(limit=1)
        
        if results:
            result = results[0]
            print(f"✅ 数据库查询成功")
            print(f"   群组: {result.group_id}")
            print(f"   主题: {result.main_topic}")
            
            # 检查新字段
            heat = getattr(result, 'discussion_heat', None)
            key_info = getattr(result, 'key_information', None)
            
            print(f"   讨论热度: {heat}")
            print(f"   关键信息: {key_info}")
            
            if heat is not None and key_info is not None:
                print("✅ 新字段查询正常")
            else:
                print("⚠️ 新字段可能为空，但查询机制正常")
        else:
            print("⚠️ 暂无分析结果数据")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库查询测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试所有修复...")
    print("=" * 50)
    
    tests = [
        test_datetime_fix,
        test_schedule_manager,
        test_api_models,
        test_database_query
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ 测试执行失败: {e}")
    
    print("\n" + "=" * 50)
    print(f"🎯 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！修复成功！")
    else:
        print("⚠️ 部分测试失败，需要进一步检查")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
