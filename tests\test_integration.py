"""
集成测试
"""

import pytest
import tempfile
import sqlite3
from datetime import datetime
from pathlib import Path
from unittest.mock import Mock, patch

from main import WechatAnalysisSystem


class TestWechatAnalysisSystemIntegration:
    """微信分析系统集成测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.temp_dir = tempfile.mkdtemp()
        self.wechat_db = Path(self.temp_dir) / "test_wechat.db"
        self.analysis_db = Path(self.temp_dir) / "test_analysis.db"
        
        # 创建测试数据库
        self._create_test_database()
        
        # 模拟配置
        self._mock_config()
    
    def teardown_method(self):
        """测试后清理"""
        import shutil
        shutil.rmtree(self.temp_dir)
    
    def _create_test_database(self):
        """创建测试数据库"""
        conn = sqlite3.connect(self.wechat_db)
        cursor = conn.cursor()
        
        # 创建MSG表
        cursor.execute('''
            CREATE TABLE MSG (
                localId INTEGER PRIMARY KEY,
                TalkerId INT,
                MsgSvrID INT,
                Type INT,
                SubType INT,
                IsSender INT,
                CreateTime INT,
                Sequence INT,
                StrTalker TEXT,
                StrContent TEXT,
                DisplayContent TEXT
            )
        ''')
        
        # 插入测试数据
        now = int(datetime.now().timestamp())
        test_messages = [
            (1, 1, 1001, 1, 0, 0, now - 1800, 1, 'test_group_1', '今天股市怎么样？', '今天股市怎么样？'),
            (2, 2, 1002, 1, 0, 0, now - 1200, 2, 'test_group_1', '000001涨了5%', '000001涨了5%'),
            (3, 3, 1003, 1, 0, 0, now - 600, 3, 'test_group_1', '看好这只股票', '看好这只股票'),
            (4, 4, 1004, 1, 0, 0, now - 300, 4, 'test_group_2', '分享一个链接 https://example.com', '分享一个链接 https://example.com'),
        ]
        
        cursor.executemany('''
            INSERT INTO MSG (localId, TalkerId, MsgSvrID, Type, SubType, IsSender, 
                           CreateTime, Sequence, StrTalker, StrContent, DisplayContent)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', test_messages)
        
        conn.commit()
        conn.close()
    
    def _mock_config(self):
        """模拟配置"""
        mock_config = Mock()
        mock_config.get.side_effect = lambda key, default=None: {
            'database.wechat_db_path': str(self.wechat_db),
            'database.analysis_db_path': str(self.analysis_db),
            'doubao_api.api_key': 'test_key',
            'doubao_api.base_url': 'https://test.api.com',
            'doubao_api.model_id': 'test-model',
            'doubao_api.timeout': 30,
            'doubao_api.max_retries': 3,
            'wechat_notification.webhook_url': 'https://test.webhook.com',
            'wechat_notification.message_template': '测试模板 {datetime} {content}',
            'analysis.time_window_hours': 1,
            'analysis.message_types': [1],
            'analysis.min_message_length': 2,
            'analysis.max_message_length': 1000,
            'deep_analysis.stock.enabled': True,
            'deep_analysis.stock.data_sources': ['test'],
            'deep_analysis.stock.score_range': [1, 10],
            'logging.level': 'INFO',
            'logging.file_path': str(Path(self.temp_dir) / 'test.log'),
            'logging.max_file_size': 10,
            'logging.backup_count': 5,
            'logging.format': '{time} | {level} | {message}'
        }.get(key, default)
        
        mock_config.get_database_config.return_value = {
            'wechat_db_path': str(self.wechat_db),
            'analysis_db_path': str(self.analysis_db)
        }
        
        mock_config.get_doubao_config.return_value = {
            'api_key': 'test_key',
            'base_url': 'https://test.api.com',
            'model_id': 'test-model',
            'timeout': 30,
            'max_retries': 3
        }
        
        mock_config.get_wechat_notification_config.return_value = {
            'webhook_url': 'https://test.webhook.com',
            'message_template': '测试模板 {datetime} {content}'
        }
        
        mock_config.get_analysis_config.return_value = {
            'time_window_hours': 1,
            'message_types': [1],
            'min_message_length': 2,
            'max_message_length': 1000
        }
        
        mock_config.get_deep_analysis_config.return_value = {
            'stock': {
                'enabled': True,
                'data_sources': ['test'],
                'score_range': [1, 10]
            }
        }
        
        mock_config.get_logging_config.return_value = {
            'level': 'INFO',
            'file_path': str(Path(self.temp_dir) / 'test.log'),
            'max_file_size': 10,
            'backup_count': 5,
            'format': '{time} | {level} | {message}'
        }
        
        # 使用patch替换get_config函数
        self.config_patcher = patch('config.get_config', return_value=mock_config)
        self.config_patcher.start()
    
    def test_system_initialization(self):
        """测试系统初始化"""
        with patch('analysis.doubao_client.DoubaoClient._make_request') as mock_request, \
             patch('notification.wechat_notifier.WechatNotifier._send_message') as mock_send:
            
            # 模拟API响应
            mock_request.return_value = '{"main_topic": "测试话题", "topic_category": "其他"}'
            mock_send.return_value = True
            
            system = WechatAnalysisSystem()
            
            # 验证各模块都已初始化
            assert system.data_processor is not None
            assert system.topic_analyzer is not None
            assert system.sentiment_analyzer is not None
            assert system.stock_analyzer is not None
            assert system.wechat_notifier is not None
            assert system.report_formatter is not None
            assert system.db_manager is not None
    
    def test_database_connectivity(self):
        """测试数据库连接"""
        with patch('analysis.doubao_client.DoubaoClient._make_request') as mock_request, \
             patch('notification.wechat_notifier.WechatNotifier._send_message') as mock_send:
            
            mock_request.return_value = '{"main_topic": "测试话题"}'
            mock_send.return_value = True
            
            system = WechatAnalysisSystem()
            
            # 测试数据库连接
            result = system._test_database()
            assert result is True
    
    def test_data_processing_flow(self):
        """测试数据处理流程"""
        with patch('analysis.doubao_client.DoubaoClient._make_request') as mock_request, \
             patch('notification.wechat_notifier.WechatNotifier._send_message') as mock_send:
            
            # 模拟豆包API响应
            mock_api_response = '''
            {
                "main_topic": "股票讨论",
                "topic_category": "股票",
                "topics": [{"topic": "股票分析", "confidence": 0.9, "keywords": ["000001", "股市"]}],
                "sentiment": {"score": 0.3, "label": "积极", "explanation": "讨论积极"},
                "key_information": ["000001涨了5%", "看好这只股票"],
                "discussion_heat": 7,
                "summary": "群组讨论股票000001的表现"
            }
            '''
            mock_request.return_value = mock_api_response
            mock_send.return_value = True
            
            system = WechatAnalysisSystem()
            
            # 获取消息数据
            messages_by_group = system.data_processor.get_recent_group_messages(hours=1)
            
            # 验证数据获取
            assert isinstance(messages_by_group, dict)
            assert len(messages_by_group) > 0
            
            # 验证消息内容
            for group_id, messages in messages_by_group.items():
                assert isinstance(messages, list)
                assert len(messages) > 0
                for message in messages:
                    assert hasattr(message, 'str_content')
                    assert hasattr(message, 'create_datetime')
    
    def test_analysis_workflow(self):
        """测试完整分析工作流"""
        with patch('analysis.doubao_client.DoubaoClient._make_request') as mock_request, \
             patch('notification.wechat_notifier.WechatNotifier._send_message') as mock_send:
            
            # 模拟豆包API响应
            mock_api_response = '''
            {
                "main_topic": "股票讨论",
                "topic_category": "股票",
                "topics": [{"topic": "股票分析", "confidence": 0.9, "keywords": ["000001"]}],
                "sentiment": {"score": 0.3, "label": "积极", "explanation": "讨论积极"},
                "key_information": ["000001涨了5%"],
                "discussion_heat": 7,
                "summary": "股票讨论"
            }
            '''
            mock_request.return_value = mock_api_response
            mock_send.return_value = True
            
            system = WechatAnalysisSystem()
            
            # 运行完整分析
            result = system.run_analysis(hours=1)
            
            # 验证分析结果
            assert result['status'] == 'success'
            assert 'processed_groups' in result
            assert 'analyzed_groups' in result
            assert 'total_messages' in result
            assert 'analysis_results' in result
            
            # 验证分析结果内容
            if result['analysis_results']:
                analysis_result = result['analysis_results'][0]
                assert 'group_id' in analysis_result
                assert 'main_topic' in analysis_result
                assert 'topic_category' in analysis_result
    
    def test_error_handling(self):
        """测试错误处理"""
        with patch('analysis.doubao_client.DoubaoClient._make_request') as mock_request, \
             patch('notification.wechat_notifier.WechatNotifier._send_message') as mock_send:
            
            # 模拟API错误
            mock_request.side_effect = Exception("API调用失败")
            mock_send.return_value = True
            
            system = WechatAnalysisSystem()
            
            # 运行分析，应该能处理错误
            result = system.run_analysis(hours=1)
            
            # 验证错误处理
            assert result['status'] in ['success', 'error']  # 可能部分成功
    
    def teardown_method(self):
        """清理"""
        if hasattr(self, 'config_patcher'):
            self.config_patcher.stop()
        
        import shutil
        shutil.rmtree(self.temp_dir)
