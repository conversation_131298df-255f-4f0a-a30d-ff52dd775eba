# 微信群聊内容智能分析系统配置文件

# 数据库配置
database:
  # 微信数据库路径
  wechat_db_path: "D:/wechat/merge_all.db"
  # 分析结果数据库路径
  analysis_db_path: "./data/analysis_results.db"

# 豆包API配置
doubao_api:
  # API密钥（建议通过环境变量设置）
  # api_key: "${ARK_API_KEY}"
  api_key: "8e8e774e-996d-400a-a3e5-44e457161541"
  # API基础URL
  base_url: "https://ark.cn-beijing.volces.com/api/v3/bots"
  # 模型ID
  model_id: "bot-20250709092323-lmvbj"
  # 请求超时时间（秒）
  timeout: 30
  # 最大重试次数
  max_retries: 3

# 企业微信通知配置
wechat_notification:
  # Webhook URL
  webhook_url: "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=6c36ed57-c489-4b9c-bb03-e64f69e3ca73"
  # 消息模板
  message_template: "📊 微信群聊分析报告\n时间：{datetime}\n\n{content}"

# 分析配置
analysis:
  # 分析时间窗口（小时）
  time_window_hours: 1
  # 消息类型过滤（只处理文本消息）
  message_types: [1]
  # 最小消息长度
  min_message_length: 2
  # 最大消息长度
  max_message_length: 1000

# 深度分析配置
deep_analysis:
  # 股票分析
  stock:
    # 是否启用股票分析
    enabled: true
    # 数据源配置
    data_sources:
      - "tushare"
      - "akshare"
    # 评分范围
    score_range: [1, 10]
  
  # 时事分析
  news:
    # 是否启用时事分析
    enabled: true
    # 搜索引擎配置
    search_engines:
      - "baidu"
      - "google"
    # 最大搜索结果数
    max_results: 10
  
  # 链接分析
  link:
    # 是否启用链接分析
    enabled: true
    # 请求超时时间
    timeout: 10
    # 用户代理
    user_agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"

# 日志配置
logging:
  # 日志级别
  level: "INFO"
  # 日志文件路径
  file_path: "./logs/wx_analysis.log"
  # 日志文件最大大小（MB）
  max_file_size: 10
  # 保留日志文件数量
  backup_count: 5
  # 日志格式
  format: "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"

# 调度配置
scheduler:
  # 是否启用定时任务
  enabled: false
  # 执行间隔（分钟）
  interval_minutes: 60
  # 立即执行一次
  run_immediately: true
