"""
定时任务管理器

负责管理和执行定时分析任务，包括：
- 定时执行分析任务
- 静默时间段控制
- 发送分析报告和词云
"""

import asyncio
import json
import threading
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Any, Optional
from loguru import logger

from database.db_manager import DatabaseManager
from main import WechatAnalysisSystem
from web.task_manager import task_manager


class ScheduleManager:
    """定时任务管理器"""
    
    def __init__(self):
        self.db_manager = DatabaseManager()
        self.analysis_system = WechatAnalysisSystem()
        self.config_file = Path("./data/schedule_config.json")
        self.running = False
        self.thread = None
        
    def load_config(self) -> Dict[str, Any]:
        """加载定时任务配置"""
        try:
            if not self.config_file.exists():
                return self._get_default_config()
            
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
                
            # 确保配置包含所有必要字段
            default_config = self._get_default_config()
            for key, value in default_config.items():
                if key not in config:
                    config[key] = value
                    
            return config
            
        except Exception as e:
            logger.error(f"加载定时任务配置失败: {e}")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "interval_minutes": 60,
            "send_analysis_report": True,
            "send_wordcloud": True,
            "silent_start": "23:00",
            "silent_end": "07:00",
            "enabled": False
        }
    
    def save_config(self, config: Dict[str, Any]) -> bool:
        """保存定时任务配置"""
        try:
            self.config_file.parent.mkdir(exist_ok=True)
            
            config["updated_at"] = datetime.now().isoformat()
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
                
            logger.info(f"定时任务配置已保存: {config}")
            return True
            
        except Exception as e:
            logger.error(f"保存定时任务配置失败: {e}")
            return False
    
    def is_in_silent_period(self, config: Dict[str, Any]) -> bool:
        """检查是否在静默时间段内"""
        try:
            now = datetime.now()
            current_time = now.strftime("%H:%M")
            
            silent_start = config.get("silent_start", "23:00")
            silent_end = config.get("silent_end", "07:00")
            
            # 处理跨天的情况
            if silent_start <= silent_end:
                # 同一天内的时间段
                return silent_start <= current_time <= silent_end
            else:
                # 跨天的时间段
                return current_time >= silent_start or current_time <= silent_end
                
        except Exception as e:
            logger.error(f"检查静默时间段失败: {e}")
            return False
    
    def start(self):
        """启动定时任务"""
        if self.running:
            logger.warning("定时任务已在运行中")
            return
            
        self.running = True
        self.thread = threading.Thread(target=self._run_schedule, daemon=True)
        self.thread.start()
        logger.info("定时任务管理器已启动")
    
    def stop(self):
        """停止定时任务"""
        self.running = False
        if self.thread:
            self.thread.join(timeout=5)
        logger.info("定时任务管理器已停止")
    
    def _run_schedule(self):
        """运行定时任务循环"""
        last_run_time = None
        
        while self.running:
            try:
                config = self.load_config()
                
                # 检查是否启用定时任务
                if not config.get("enabled", False):
                    time.sleep(60)  # 每分钟检查一次配置
                    continue
                
                # 检查是否在静默时间段
                if self.is_in_silent_period(config):
                    logger.debug("当前在静默时间段内，跳过任务执行")
                    time.sleep(60)
                    continue
                
                # 检查是否到了执行时间
                interval_minutes = config.get("interval_minutes", 60)
                now = datetime.now()
                
                if last_run_time is None:
                    # 首次运行
                    should_run = True
                else:
                    time_since_last_run = (now - last_run_time).total_seconds() / 60
                    should_run = time_since_last_run >= interval_minutes
                
                if should_run:
                    logger.info("开始执行定时分析任务")
                    self._execute_scheduled_analysis(config)
                    last_run_time = now
                
                # 每分钟检查一次
                time.sleep(60)
                
            except Exception as e:
                logger.error(f"定时任务执行出错: {e}")
                time.sleep(60)
    
    def _execute_scheduled_analysis(self, config: Dict[str, Any]):
        """执行定时分析任务"""
        try:
            # 创建分析任务
            task_id = task_manager.create_analysis_task(
                hours=1,  # 分析最近1小时的数据
                sync_data=True,
                generate_wordcloud=config.get("send_wordcloud", True),
                send_wordcloud_notification=config.get("send_wordcloud", True),
                send_analysis_report=config.get("send_analysis_report", True)
            )
            
            logger.info(f"定时分析任务已创建: {task_id}")
            
        except Exception as e:
            logger.error(f"执行定时分析任务失败: {e}")


# 全局定时任务管理器实例
schedule_manager = ScheduleManager()


def start_schedule_manager():
    """启动定时任务管理器"""
    schedule_manager.start()


def stop_schedule_manager():
    """停止定时任务管理器"""
    schedule_manager.stop()


def get_schedule_config():
    """获取定时任务配置"""
    return schedule_manager.load_config()


def save_schedule_config(config: Dict[str, Any]):
    """保存定时任务配置"""
    return schedule_manager.save_config(config)
