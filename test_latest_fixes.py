#!/usr/bin/env python3
"""
测试最新修复的脚本

验证以下修复是否正常工作：
1. 群组管理-启用分析和禁用分析数据库落地
2. 微信通知报错修复
3. 首页分析报告按热度排序
4. 词云图片大小调整
5. 显示group_name而不是group_id
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_group_analysis_status():
    """测试群组分析状态数据库更新"""
    print("🧪 测试群组分析状态数据库更新...")
    try:
        from database.db_manager import DatabaseManager
        
        db_manager = DatabaseManager()
        
        # 测试设置群组分析状态
        test_group_id = "test_group_12345"
        
        # 启用分析
        success1 = db_manager.set_group_analysis_status(test_group_id, True)
        status1 = db_manager.get_group_analysis_status(test_group_id)
        
        # 禁用分析
        success2 = db_manager.set_group_analysis_status(test_group_id, False)
        status2 = db_manager.get_group_analysis_status(test_group_id)
        
        if success1 and success2 and status1 == True and status2 == False:
            print("✅ 群组分析状态数据库更新正常")
            return True
        else:
            print(f"❌ 群组分析状态更新异常: success1={success1}, status1={status1}, success2={success2}, status2={status2}")
            return False
            
    except Exception as e:
        print(f"❌ 群组分析状态测试失败: {e}")
        return False

def test_wechat_notifier_init():
    """测试微信通知器初始化"""
    print("\n🧪 测试微信通知器初始化...")
    try:
        from notification.wechat_notifier import WechatNotifier
        
        # 尝试初始化通知器（即使没有配置也不应该抛出异常）
        notifier = WechatNotifier()
        
        # 检查是否正确设置了enabled状态
        has_enabled = hasattr(notifier, 'enabled')
        
        if has_enabled:
            print(f"✅ 微信通知器初始化成功，enabled状态: {notifier.enabled}")
            
            # 测试发送方法（应该在未启用时直接返回True）
            if not notifier.enabled:
                result = notifier.send_analysis_report([], {})
                if result:
                    print("✅ 未启用时发送方法正确返回True")
                    return True
                else:
                    print("❌ 未启用时发送方法返回False")
                    return False
            else:
                print("✅ 通知器已启用，跳过发送测试")
                return True
        else:
            print("❌ 通知器缺少enabled属性")
            return False
            
    except Exception as e:
        print(f"❌ 微信通知器测试失败: {e}")
        return False

def test_analysis_results_sorting():
    """测试分析结果排序"""
    print("\n🧪 测试分析结果排序...")
    try:
        from database.db_manager import DatabaseManager
        
        db_manager = DatabaseManager()
        
        # 获取分析结果
        results = db_manager.get_analysis_results(hours=24, limit=10)
        
        if not results:
            print("⚠️ 暂无分析结果数据，无法测试排序")
            return True
        
        # 检查是否有discussion_heat字段
        has_heat = any(hasattr(result, 'discussion_heat') for result in results)
        
        if has_heat:
            # 手动排序测试
            sorted_results = sorted(results, key=lambda x: getattr(x, 'discussion_heat', 0), reverse=True)
            
            print(f"✅ 分析结果排序功能正常，共{len(results)}条结果")
            
            # 显示前3个结果的热度
            for i, result in enumerate(sorted_results[:3]):
                heat = getattr(result, 'discussion_heat', 0)
                print(f"   第{i+1}名: 热度 {heat}")
            
            return True
        else:
            print("⚠️ 分析结果缺少discussion_heat字段")
            return True
            
    except Exception as e:
        print(f"❌ 分析结果排序测试失败: {e}")
        return False

def test_group_name_display():
    """测试群组名称显示"""
    print("\n🧪 测试群组名称显示...")
    try:
        from database.db_manager import DatabaseManager
        
        db_manager = DatabaseManager()
        
        # 获取活跃群组
        groups = db_manager.get_active_groups()
        
        if not groups:
            print("⚠️ 暂无群组数据")
            return True
        
        # 检查群组是否有名称
        groups_with_names = [g for g in groups if g.group_name]
        groups_without_names = [g for g in groups if not g.group_name]
        
        print(f"✅ 群组数据获取正常")
        print(f"   有名称的群组: {len(groups_with_names)}")
        print(f"   无名称的群组: {len(groups_without_names)}")
        
        # 显示前3个群组的信息
        for i, group in enumerate(groups[:3]):
            display_name = group.group_name or group.group_id[:30] + "..."
            print(f"   群组{i+1}: {display_name}")
        
        return True
        
    except Exception as e:
        print(f"❌ 群组名称显示测试失败: {e}")
        return False

def test_api_models():
    """测试API数据模型"""
    print("\n🧪 测试API数据模型...")
    try:
        from web.api_server import GroupConfigRequest
        
        # 测试群组配置请求
        config_req = GroupConfigRequest(
            group_id="test_group",
            group_name="测试群组",
            need_analysis=False
        )
        
        # 检查字段是否正确
        if (config_req.group_id == "test_group" and 
            config_req.group_name == "测试群组" and 
            config_req.need_analysis == False):
            print("✅ API数据模型正常")
            return True
        else:
            print("❌ API数据模型字段异常")
            return False
            
    except Exception as e:
        print(f"❌ API数据模型测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试最新修复...")
    print("=" * 60)
    
    tests = [
        test_group_analysis_status,
        test_wechat_notifier_init,
        test_analysis_results_sorting,
        test_group_name_display,
        test_api_models
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ 测试执行失败: {e}")
    
    print("\n" + "=" * 60)
    print(f"🎯 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！修复成功！")
        print("\n✅ 修复验证:")
        print("   1. 群组管理数据库更新 - 正常")
        print("   2. 微信通知报错修复 - 正常")
        print("   3. 分析结果排序功能 - 正常")
        print("   4. 群组名称显示逻辑 - 正常")
        print("   5. API数据模型更新 - 正常")
    else:
        print("⚠️ 部分测试失败，需要进一步检查")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
