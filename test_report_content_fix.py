#!/usr/bin/env python3
"""
测试分析报告内容截断修复效果
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_key_information_display():
    """测试关键信息显示"""
    print("🧪 测试关键信息显示...")
    
    try:
        from notification.wechat_notifier import WechatNotifier
        
        notifier = WechatNotifier()
        
        # 模拟包含长关键信息的分析结果
        analysis_results = [
            {
                'group_id': 'test_group_key_info',
                'main_topic': '测试长关键信息显示',
                'sentiment_score': 0.5,
                'discussion_heat': 7.0,
                'topic_category': 'test',
                'topics': ['话题1', '话题2', '话题3'],
                'key_information': [
                    '这是第一条关键信息，包含了很多重要的详细内容，不应该被截断显示',
                    '这是第二条关键信息，同样包含了大量的重要信息，需要完整显示给用户',
                    '这是第三条关键信息，提供了额外的重要背景和分析结果',
                    '这是第四条关键信息，虽然可能不会全部显示，但前面的应该完整'
                ]
            }
        ]
        
        summary = {
            'total_groups': 1,
            'total_messages': 50,
            'time_window_hours': 1
        }
        
        # 格式化报告
        report_content = notifier._format_analysis_report(analysis_results, summary)
        
        print("生成的报告内容:")
        print("=" * 60)
        print(report_content)
        print("=" * 60)
        
        # 检查关键信息是否完整显示
        if "这是第一条关键信息，包含了很多重要的详细内容，不应该被截断显示" in report_content:
            print("✅ 第一条关键信息完整显示")
        else:
            print("❌ 第一条关键信息被截断")
            return False
        
        if "这是第二条关键信息" in report_content:
            print("✅ 第二条关键信息显示")
        else:
            print("❌ 第二条关键信息未显示")
            return False
        
        # 检查是否有"..."截断标记
        if "..." in report_content and "还有" in report_content:
            print("✅ 正确显示了更多信息的提示")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_topic_details_display():
    """测试话题详情显示"""
    print("\n🧪 测试话题详情显示...")
    
    try:
        from notification.wechat_notifier import WechatNotifier
        
        notifier = WechatNotifier()
        
        # 模拟包含多个话题的分析结果
        analysis_results = [
            {
                'group_id': 'test_group_topics',
                'main_topic': '测试多话题显示',
                'sentiment_score': 0.3,
                'discussion_heat': 8.0,
                'topic_category': 'news',
                'topics': [
                    {
                        'topic': '股票市场分析',
                        'keywords': ['股价', '涨跌', '投资', '风险', '收益']
                    },
                    {
                        'topic': '科技发展趋势',
                        'keywords': ['AI', '人工智能', '机器学习', '深度学习', '算法']
                    },
                    {
                        'topic': '经济政策解读',
                        'keywords': ['政策', '经济', '发展', '改革', '创新']
                    },
                    '简单话题1',
                    '简单话题2',
                    '简单话题3'
                ],
                'key_information': ['重要信息1']
            }
        ]
        
        summary = {
            'total_groups': 1,
            'total_messages': 100,
            'time_window_hours': 2
        }
        
        # 格式化报告
        report_content = notifier._format_analysis_report(analysis_results, summary)
        
        print("生成的报告内容:")
        print("=" * 60)
        print(report_content)
        print("=" * 60)
        
        # 检查话题详情是否完整显示
        if "股票市场分析：股价, 涨跌, 投资, 风险, 收益" in report_content:
            print("✅ 第一个话题的关键词完整显示")
        else:
            print("❌ 第一个话题的关键词被截断")
            return False
        
        if "科技发展趋势" in report_content:
            print("✅ 第二个话题显示")
        else:
            print("❌ 第二个话题未显示")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_deep_analysis_display():
    """测试深度分析显示"""
    print("\n🧪 测试深度分析显示...")
    
    try:
        from notification.wechat_notifier import WechatNotifier
        
        notifier = WechatNotifier()
        
        # 模拟包含深度分析的结果
        analysis_results = [
            {
                'group_id': 'test_group_deep',
                'main_topic': '测试深度分析显示',
                'sentiment_score': 0.7,
                'discussion_heat': 9.0,
                'topic_category': 'stock',
                'topics': ['股票分析'],
                'key_information': ['股票相关信息'],
                'deep_analysis': {
                    'stock_analysis': {
                        'stocks': [
                            {
                                'name': '腾讯控股',
                                'code': '00700.HK',
                                'investment_score': 8.5
                            },
                            {
                                'name': '阿里巴巴',
                                'code': '09988.HK',
                                'investment_score': 7.8
                            }
                        ],
                        'overall_analysis': {
                            'average_investment_score': 8.15
                        }
                    },
                    'news_analysis': {
                        'events': [
                            {
                                'title': '科技股大涨，市场情绪乐观',
                                'impact': 'positive'
                            },
                            {
                                'title': '监管政策调整，影响科技行业发展前景',
                                'impact': 'neutral'
                            }
                        ]
                    },
                    'link_analysis': {
                        'links': [
                            {'type': '新闻', 'url': 'http://example1.com'},
                            {'type': '分析', 'url': 'http://example2.com'},
                            {'type': '新闻', 'url': 'http://example3.com'}
                        ]
                    }
                }
            }
        ]
        
        summary = {
            'total_groups': 1,
            'total_messages': 80,
            'time_window_hours': 1
        }
        
        # 格式化报告
        report_content = notifier._format_analysis_report(analysis_results, summary)
        
        print("生成的报告内容:")
        print("=" * 60)
        print(report_content)
        print("=" * 60)
        
        # 检查深度分析是否显示
        if "投资评分：⭐ 8.15/10" in report_content:
            print("✅ 投资评分显示")
        else:
            print("❌ 投资评分未显示")
            return False
        
        if "腾讯控股: 8.5/10" in report_content:
            print("✅ 具体股票信息显示")
        else:
            print("❌ 具体股票信息未显示")
            return False
        
        if "相关事件：📅 2 个" in report_content:
            print("✅ 新闻事件统计显示")
        else:
            print("❌ 新闻事件统计未显示")
            return False
        
        if "分享链接：🔗 3 个" in report_content:
            print("✅ 链接统计显示")
        else:
            print("❌ 链接统计未显示")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_long_message_handling():
    """测试长消息处理"""
    print("\n🧪 测试长消息处理...")
    
    try:
        from notification.wechat_notifier import WechatNotifier
        
        notifier = WechatNotifier()
        
        # 创建一个很长的消息
        long_content = "📊 分析摘要\n"
        long_content += "• 分析群组：10 个\n"
        long_content += "• 处理消息：1000 条\n"
        long_content += "• 时间窗口：1 小时\n\n"
        
        # 添加多个群组的详细分析
        for i in range(10):
            long_content += f"🔍 群组分析 {i+1}\n"
            long_content += f"群组：测试群组{i+1}\n"
            long_content += f"话题：💬 这是一个很长的话题描述，包含了大量的详细信息和分析结果，用于测试长消息的处理能力\n"
            long_content += f"情感：😊 0.{i}5\n"
            long_content += f"热度：🔥 {8+i%3}/10\n"
            long_content += "关键信息：\n"
            long_content += f"  1. 这是第一条关键信息，包含了很多重要的详细内容，不应该被截断显示\n"
            long_content += f"  2. 这是第二条关键信息，同样包含了大量的重要信息，需要完整显示给用户\n"
            long_content += f"  3. 这是第三条关键信息，提供了额外的重要背景和分析结果\n"
            long_content += "\n"
        
        print(f"原始消息长度: {len(long_content)} 字符")
        
        # 测试长消息分割
        if len(long_content) > 2048:
            print("✅ 消息长度超过限制，将测试分割功能")
            
            # 模拟发送（不实际发送）
            send_count = 0
            original_send_raw = notifier._send_message_raw
            
            def mock_send_raw(message):
                nonlocal send_count
                send_count += 1
                content = message['text']['content']
                print(f"模拟发送第{send_count}条消息，长度: {len(content)} 字符")
                print(f"消息开头: {content[:100]}...")
                return True
            
            notifier._send_message_raw = mock_send_raw
            
            # 测试发送
            result = notifier._send_message(long_content)
            
            # 恢复原始方法
            notifier._send_message_raw = original_send_raw
            
            if result and send_count > 1:
                print(f"✅ 长消息成功分割为{send_count}条发送")
                return True
            else:
                print("❌ 长消息分割失败")
                return False
        else:
            print("✅ 消息长度在限制内")
            return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试分析报告内容截断修复效果...")
    print("=" * 60)
    
    tests = [
        ("关键信息显示测试", test_key_information_display),
        ("话题详情显示测试", test_topic_details_display),
        ("深度分析显示测试", test_deep_analysis_display),
        ("长消息处理测试", test_long_message_handling)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            print(f"\n📋 测试: {test_name}")
            if test_func():
                passed += 1
                print(f"   ✅ {test_name} - 通过")
            else:
                print(f"   ❌ {test_name} - 失败")
        except Exception as e:
            print(f"   ❌ {test_name} - 执行失败: {e}")
    
    print("\n" + "=" * 60)
    print(f"🎯 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 分析报告内容截断问题修复成功！")
        print("\n✅ 修复总结:")
        print("1. 关键信息不再被截断，完整显示前3条")
        print("2. 话题详情显示更多内容，支持分行显示")
        print("3. 深度分析显示具体详情，不只是统计")
        print("4. 长消息自动分割发送，避免被截断")
        print("5. 建议数量增加到5条，提供更多指导")
    else:
        print("⚠️ 部分测试失败，需要进一步调查")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
