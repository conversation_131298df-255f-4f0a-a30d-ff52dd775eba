"""
词云分析器

负责生成群聊内容的词云图片，提供可视化的关键词分析。
"""

import os
import hashlib
import base64
from typing import Dict, List, Any, Optional
from datetime import datetime
from pathlib import Path
import jieba
import jieba.analyse
from wordcloud import WordCloud
from PIL import Image
import numpy as np
from loguru import logger

from config import get_config


class WordCloudAnalyzer:
    """词云分析器类"""
    
    def __init__(self):
        """初始化词云分析器"""
        self.config = get_config()
        
        # 词云配置
        self.output_dir = Path("./data/wordclouds")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 词云样式配置
        self.wordcloud_config = {
            'width': 800,
            'height': 600,
            'background_color': 'white',
            'max_words': 100,
            'font_path': self._get_font_path(),
            'colormap': 'viridis',
            'relative_scaling': 0.5,
            'min_font_size': 10
        }
        
        # 停用词
        self.stop_words = self._load_stop_words()
        
        logger.info("词云分析器初始化完成")
    
    def _get_font_path(self) -> Optional[str]:
        """获取中文字体路径"""
        # 常见的中文字体路径
        font_paths = [
            "C:/Windows/Fonts/simhei.ttf",  # 黑体
            "C:/Windows/Fonts/simsun.ttc",  # 宋体
            "C:/Windows/Fonts/msyh.ttc",    # 微软雅黑
            "/System/Library/Fonts/PingFang.ttc",  # macOS
            "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf"  # Linux
        ]
        
        for font_path in font_paths:
            if os.path.exists(font_path):
                return font_path
        
        logger.warning("未找到中文字体，词云可能无法正确显示中文")
        return None
    
    def _load_stop_words(self) -> set:
        """加载停用词"""
        stop_words = {
            # 基础停用词
            '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个',
            '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好',
            '自己', '这', '那', '里', '就是', '还是', '比较', '觉得', '可以', '应该',
            # 群聊常见词
            '哈哈', '嗯嗯', '哦', '啊', '呃', '额', '嗯', '哈', '呵呵', '嘿嘿',
            '大家', '群里', '群友', '各位', '兄弟', '朋友',
            # 标点和符号
            '。', '，', '！', '？', '；', '：', '"', '"', ''', ''', '（', '）', '【', '】',
            '、', '…', '—', '～', '·'
        }
        
        return stop_words
    
    def generate_wordcloud(self, messages: List[str], group_id: str, 
                         custom_config: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        生成词云图片
        
        Args:
            messages: 消息内容列表
            group_id: 群组ID
            custom_config: 自定义配置
            
        Returns:
            词云生成结果
        """
        try:
            logger.info(f"开始为群组 {group_id} 生成词云")
            
            # 合并所有消息文本
            text = ' '.join(messages)
            
            if not text.strip():
                return {
                    'success': False,
                    'error': '没有有效的文本内容',
                    'group_id': group_id
                }
            
            # 分词和词频统计
            word_freq = self._extract_keywords(text)
            
            if not word_freq:
                return {
                    'success': False,
                    'error': '没有提取到有效关键词',
                    'group_id': group_id
                }
            
            # 生成词云配置
            config = self.wordcloud_config.copy()
            if custom_config:
                config.update(custom_config)
            
            # 创建词云对象
            wordcloud = WordCloud(**config)
            
            # 生成词云
            wordcloud.generate_from_frequencies(word_freq)
            
            # 保存图片
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"wordcloud_{group_id}_{timestamp}.png"
            filepath = self.output_dir / filename
            
            wordcloud.to_file(str(filepath))
            
            # 计算文件MD5
            file_md5 = self._calculate_file_md5(filepath)
            
            # 转换为base64（用于企业微信发送）
            image_base64 = self._image_to_base64(filepath)
            
            result = {
                'success': True,
                'group_id': group_id,
                'filepath': str(filepath),
                'filename': filename,
                'md5': file_md5,
                'base64': image_base64,
                'word_count': len(word_freq),
                'top_words': list(word_freq.items())[:20],
                'generated_at': datetime.now().isoformat()
            }
            
            logger.info(f"词云生成成功: {filename}")
            return result
            
        except Exception as e:
            logger.error(f"词云生成失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'group_id': group_id
            }
    
    def _extract_keywords(self, text: str, top_k: int = 100) -> Dict[str, float]:
        """
        提取关键词和词频
        
        Args:
            text: 文本内容
            top_k: 返回前k个关键词
            
        Returns:
            词频字典
        """
        # 使用jieba分词
        words = jieba.cut(text)
        
        # 过滤停用词和短词
        filtered_words = []
        for word in words:
            word = word.strip()
            if (len(word) >= 2 and 
                word not in self.stop_words and 
                not word.isdigit() and
                not self._is_english_only(word)):
                filtered_words.append(word)
        
        # 统计词频
        word_freq = {}
        for word in filtered_words:
            word_freq[word] = word_freq.get(word, 0) + 1
        
        # 按频率排序并返回前top_k个
        sorted_words = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)
        return dict(sorted_words[:top_k])
    
    def _is_english_only(self, text: str) -> bool:
        """判断是否为纯英文"""
        return text.isascii() and text.isalpha()
    
    def _calculate_file_md5(self, filepath: Path) -> str:
        """计算文件MD5值"""
        hash_md5 = hashlib.md5()
        with open(filepath, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    
    def _image_to_base64(self, filepath: Path) -> str:
        """将图片转换为base64编码"""
        with open(filepath, "rb") as image_file:
            encoded_string = base64.b64encode(image_file.read()).decode('utf-8')
        return encoded_string
    
    def analyze_word_trends(self, word_freq_history: List[Dict[str, float]]) -> Dict[str, Any]:
        """
        分析词频趋势
        
        Args:
            word_freq_history: 历史词频数据列表
            
        Returns:
            趋势分析结果
        """
        if len(word_freq_history) < 2:
            return {'error': '需要至少2个时间点的数据'}
        
        # 获取所有出现过的词
        all_words = set()
        for word_freq in word_freq_history:
            all_words.update(word_freq.keys())
        
        # 分析趋势
        trending_up = []
        trending_down = []
        stable_words = []
        
        for word in all_words:
            frequencies = [word_freq.get(word, 0) for word_freq in word_freq_history]
            
            if len(frequencies) >= 2:
                recent_freq = frequencies[-1]
                previous_freq = frequencies[-2]
                
                if recent_freq > previous_freq * 1.5:  # 增长50%以上
                    trending_up.append((word, recent_freq, previous_freq))
                elif recent_freq < previous_freq * 0.5:  # 下降50%以上
                    trending_down.append((word, recent_freq, previous_freq))
                else:
                    stable_words.append((word, recent_freq))
        
        # 按变化幅度排序
        trending_up.sort(key=lambda x: x[1] - x[2], reverse=True)
        trending_down.sort(key=lambda x: x[2] - x[1], reverse=True)
        
        return {
            'trending_up': trending_up[:10],
            'trending_down': trending_down[:10],
            'stable_count': len(stable_words),
            'total_words': len(all_words),
            'analysis_time': datetime.now().isoformat()
        }
    
    def cleanup_old_files(self, days_to_keep: int = 7):
        """
        清理旧的词云文件
        
        Args:
            days_to_keep: 保留天数
        """
        try:
            cutoff_time = datetime.now().timestamp() - (days_to_keep * 24 * 3600)
            
            deleted_count = 0
            for file_path in self.output_dir.glob("wordcloud_*.png"):
                if file_path.stat().st_mtime < cutoff_time:
                    file_path.unlink()
                    deleted_count += 1
            
            logger.info(f"清理了 {deleted_count} 个旧词云文件")
            
        except Exception as e:
            logger.error(f"清理词云文件失败: {e}")
    
    def get_wordcloud_config(self) -> Dict[str, Any]:
        """获取词云配置"""
        return self.wordcloud_config.copy()
    
    def set_wordcloud_config(self, config: Dict[str, Any]):
        """设置词云配置"""
        self.wordcloud_config.update(config)
        logger.info("词云配置已更新")
